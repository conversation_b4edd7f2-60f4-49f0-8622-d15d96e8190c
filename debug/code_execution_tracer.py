#!/usr/bin/env python3
"""
Magentic-One 代码执行路径追踪器
=============================

这个模块提供了对Magentic-One系统中代码执行路径的深度追踪功能。
它能够：
1. 追踪每个函数调用的完整调用栈
2. 记录函数执行时间和性能指标
3. 监控函数参数和返回值
4. 生成详细的执行路径映射
5. 识别性能瓶颈和异常路径

作者: Claude Code  
创建时间: 2025-07-31
"""

import asyncio
import json
import logging
import inspect
import traceback
import functools
import sys
import threading
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Callable, Union
from dataclasses import dataclass, asdict
import ast
import dis
import gc
import psutil
import os

# 性能和系统监控
import resource

@dataclass
class FunctionCall:
    """函数调用记录"""
    call_id: str
    timestamp: str
    function_name: str
    module_name: str
    file_path: str
    line_number: int
    class_name: Optional[str]
    args: Dict[str, Any]
    kwargs: Dict[str, Any]
    caller_info: Dict[str, Any]
    thread_id: int
    process_id: int

@dataclass
class FunctionExit:
    """函数退出记录"""
    call_id: str
    timestamp: str
    return_value: Any
    exception: Optional[Dict[str, Any]]
    execution_time_ms: float
    memory_usage_mb: float
    cpu_usage_percent: float

@dataclass
class ExecutionPath:
    """执行路径"""
    path_id: str
    start_time: str
    end_time: Optional[str]
    function_calls: List[FunctionCall]
    function_exits: List[FunctionExit]
    call_tree: Dict[str, Any]
    performance_metrics: Dict[str, Any]
    total_execution_time_ms: Optional[float] = None

@dataclass
class PerformanceMetrics:
    """性能指标"""
    total_functions_called: int
    average_execution_time_ms: float
    slowest_functions: List[Tuple[str, float]]
    memory_peak_mb: float
    cpu_peak_percent: float
    exception_count: int
    call_depth_max: int

class CodeExecutionTracer:
    """代码执行路径追踪器"""
    
    def __init__(self, scenario_id: str, output_dir: Path = None):
        self.scenario_id = scenario_id
        self.output_dir = output_dir or Path("debug")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 追踪状态
        self.is_tracing = False
        self.current_path: Optional[ExecutionPath] = None
        self.call_counter = 0
        self.call_stack: List[str] = []  # 调用栈
        self.active_calls: Dict[str, FunctionCall] = {}  # 活跃的函数调用
        
        # 性能监控
        self.process = psutil.Process()
        self.start_memory = 0
        self.peak_memory = 0
        self.start_cpu_time = 0
        
        # 过滤配置
        self.include_patterns = [
            'autogen',
            'magentic_one', 
            'orchestrator',
            'websurfer',
            'filesurfer',
            'coder'
        ]
        self.exclude_patterns = [
            'site-packages',
            'python3.',
            '_pydev',
            '__pycache__',
            'logging',
            'threading',
            'asyncio'
        ]
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志记录"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        log_file = self.output_dir / f"code_execution_trace_{self.scenario_id}_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger("CodeExecutionTracer")
        self.logger.info(f"代码执行路径追踪器初始化 - 场景 {self.scenario_id}")
        
    def generate_call_id(self) -> str:
        """生成唯一的调用ID"""
        self.call_counter += 1
        return f"call_{self.call_counter:06d}_{int(time.time() * 1000000) % 1000000}"
        
    def should_trace_function(self, filename: str, function_name: str) -> bool:
        """判断是否应该追踪此函数"""
        # 排除系统和第三方库
        for pattern in self.exclude_patterns:
            if pattern in filename.lower():
                return False
                
        # 包含感兴趣的模块
        filename_lower = filename.lower()
        for pattern in self.include_patterns:
            if pattern in filename_lower:
                return True
                
        # 排除内置函数和特殊方法
        if function_name.startswith('_') and function_name.endswith('_'):
            return False
            
        # 包含项目根目录下的文件
        try:
            if '/mnt/v-mingm/AutoDebugging' in filename:
                return True
        except:
            pass
            
        return False
        
    def get_function_signature(self, frame) -> Tuple[str, Dict[str, Any], Dict[str, Any]]:
        """获取函数签名和参数"""
        try:
            # 获取函数对象
            func_name = frame.f_code.co_name
            
            # 获取参数
            args_info = {}
            kwargs_info = {}
            
            # 从帧的局部变量中提取参数
            local_vars = frame.f_locals.copy()
            
            # 获取参数名
            arg_names = frame.f_code.co_varnames[:frame.f_code.co_argcount]
            
            for arg_name in arg_names:
                if arg_name in local_vars:
                    try:
                        value = local_vars[arg_name]
                        # 安全地序列化参数值
                        serialized_value = self._safe_serialize(value)
                        if arg_name == 'self':
                            args_info[arg_name] = f"<{type(value).__name__} object>"
                        else:
                            args_info[arg_name] = serialized_value
                    except Exception as e:
                        args_info[arg_name] = f"<serialization_error: {str(e)}>"
                        
            return func_name, args_info, kwargs_info
            
        except Exception as e:
            return frame.f_code.co_name, {}, {}
            
    def _safe_serialize(self, obj: Any, max_length: int = 200) -> Any:
        """安全地序列化对象"""
        try:
            if obj is None:
                return None
            elif isinstance(obj, (str, int, float, bool)):
                if isinstance(obj, str) and len(obj) > max_length:
                    return obj[:max_length] + "..."
                return obj
            elif isinstance(obj, (list, tuple)):
                if len(obj) > 10:
                    return f"<{type(obj).__name__} with {len(obj)} items>"
                return [self._safe_serialize(item, max_length//2) for item in obj[:3]]
            elif isinstance(obj, dict):
                if len(obj) > 10:
                    return f"<dict with {len(obj)} keys>"
                result = {}
                for k, v in list(obj.items())[:3]:
                    result[str(k)] = self._safe_serialize(v, max_length//2)
                return result
            else:
                return f"<{type(obj).__name__} object>"
        except:
            return f"<{type(obj).__name__}>"
            
    def get_caller_info(self, frame) -> Dict[str, Any]:
        """获取调用者信息"""
        try:
            caller_frame = frame.f_back
            if caller_frame:
                return {
                    "function": caller_frame.f_code.co_name,
                    "file": caller_frame.f_code.co_filename,
                    "line": caller_frame.f_lineno,
                    "module": caller_frame.f_globals.get('__name__', '<unknown>')
                }
        except:
            pass
        return {"function": "<unknown>", "file": "<unknown>", "line": 0, "module": "<unknown>"}
        
    def trace_calls(self, frame, event, arg):
        """主要的追踪函数"""
        if not self.is_tracing or not self.current_path:
            return
            
        try:
            filename = frame.f_code.co_filename
            function_name = frame.f_code.co_name
            
            # 检查是否应该追踪
            if not self.should_trace_function(filename, function_name):
                return
                
            if event == 'call':
                self._handle_function_call(frame)
            elif event == 'return':
                self._handle_function_return(frame, arg)
            elif event == 'exception':
                self._handle_function_exception(frame, arg)
                
        except Exception as e:
            # 追踪器本身不应该影响被追踪的代码
            self.logger.debug(f"追踪过程中出错: {e}")
            
        return self.trace_calls
        
    def _handle_function_call(self, frame):
        """处理函数调用事件"""
        call_id = self.generate_call_id()
        timestamp = datetime.now(timezone.utc).isoformat()
        
        # 获取函数信息
        filename = frame.f_code.co_filename
        function_name = frame.f_code.co_name
        line_number = frame.f_lineno
        module_name = frame.f_globals.get('__name__', '<unknown>')
        
        # 获取类名
        class_name = None
        if 'self' in frame.f_locals:
            try:
                class_name = frame.f_locals['self'].__class__.__name__
            except:
                pass
                
        # 获取函数参数
        func_name, args, kwargs = self.get_function_signature(frame)
        
        # 获取调用者信息
        caller_info = self.get_caller_info(frame)
        
        # 创建函数调用记录
        function_call = FunctionCall(
            call_id=call_id,
            timestamp=timestamp,
            function_name=function_name,
            module_name=module_name,
            file_path=filename,
            line_number=line_number,
            class_name=class_name,
            args=args,
            kwargs=kwargs,
            caller_info=caller_info,
            thread_id=threading.get_ident(),
            process_id=os.getpid()
        )
        
        # 添加到追踪记录
        self.current_path.function_calls.append(function_call)
        self.active_calls[call_id] = function_call
        self.call_stack.append(call_id)
        
        # 记录日志
        caller_str = f"{caller_info['function']}() at {Path(caller_info['file']).name}:{caller_info['line']}"
        self.logger.debug(f"CALL [{len(self.call_stack)}] {class_name}.{function_name}() <- {caller_str}")
        
    def _handle_function_return(self, frame, return_value):
        """处理函数返回事件"""
        if not self.call_stack:
            return
            
        call_id = self.call_stack.pop()
        if call_id not in self.active_calls:
            return
            
        function_call = self.active_calls[call_id]
        timestamp = datetime.now(timezone.utc).isoformat()
        
        # 计算执行时间
        start_time = datetime.fromisoformat(function_call.timestamp.replace('Z', '+00:00'))
        end_time = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        execution_time_ms = (end_time - start_time).total_seconds() * 1000
        
        # 获取系统资源使用情况
        try:
            memory_info = self.process.memory_info()
            memory_usage_mb = memory_info.rss / 1024 / 1024
            cpu_percent = self.process.cpu_percent()
        except:
            memory_usage_mb = 0
            cpu_percent = 0
            
        # 安全地序列化返回值
        safe_return_value = self._safe_serialize(return_value)
        
        # 创建函数退出记录
        function_exit = FunctionExit(
            call_id=call_id,
            timestamp=timestamp,
            return_value=safe_return_value,
            exception=None,
            execution_time_ms=execution_time_ms,
            memory_usage_mb=memory_usage_mb,
            cpu_usage_percent=cpu_percent
        )
        
        self.current_path.function_exits.append(function_exit)
        del self.active_calls[call_id]
        
        # 更新峰值内存
        if memory_usage_mb > self.peak_memory:
            self.peak_memory = memory_usage_mb
            
        # 记录慢函数
        if execution_time_ms > 100:  # 超过100ms的函数
            self.logger.debug(f"SLOW [{execution_time_ms:.1f}ms] {function_call.class_name}.{function_call.function_name}()")
            
    def _handle_function_exception(self, frame, exc_info):
        """处理函数异常事件"""
        if not self.call_stack:
            return
            
        call_id = self.call_stack[-1]  # 不弹出，异常后还会有return事件
        if call_id not in self.active_calls:
            return
            
        function_call = self.active_calls[call_id]
        
        # 记录异常信息
        exc_type, exc_value, exc_traceback = exc_info
        exception_info = {
            "type": exc_type.__name__ if exc_type else "Unknown", 
            "message": str(exc_value) if exc_value else "",
            "traceback": traceback.format_exception(exc_type, exc_value, exc_traceback)[-5:]  # 最后5行
        }
        
        self.logger.warning(f"EXCEPTION in {function_call.class_name}.{function_call.function_name}(): {exception_info['type']}: {exception_info['message']}")
        
    def start_tracing(self, command: str) -> str:
        """开始执行路径追踪"""
        if self.is_tracing:
            self.logger.warning("追踪已在进行中")
            return self.current_path.path_id
            
        # 初始化追踪状态
        path_id = f"path_{int(time.time())}_{self.scenario_id}"
        self.current_path = ExecutionPath(
            path_id=path_id,
            start_time=datetime.now(timezone.utc).isoformat(),
            end_time=None,
            function_calls=[],
            function_exits=[],
            call_tree={},
            performance_metrics={}
        )
        
        # 重置计数器和状态
        self.call_counter = 0
        self.call_stack.clear()
        self.active_calls.clear()
        
        # 记录起始系统状态
        try:
            memory_info = self.process.memory_info()
            self.start_memory = memory_info.rss / 1024 / 1024
            self.peak_memory = self.start_memory
            self.start_cpu_time = self.process.cpu_times().user
        except:
            self.start_memory = 0
            self.peak_memory = 0
            self.start_cpu_time = 0
            
        # 启动追踪
        self.is_tracing = True
        sys.settrace(self.trace_calls)
        
        self.logger.info(f"开始执行路径追踪: {path_id} for command: {command[:100]}")
        return path_id
        
    def stop_tracing(self) -> Optional[ExecutionPath]:
        """停止执行路径追踪"""
        if not self.is_tracing or not self.current_path:
            self.logger.warning("没有活跃的追踪会话")
            return None
            
        # 停止追踪
        sys.settrace(None)
        self.is_tracing = False
        
        # 完成当前路径记录
        self.current_path.end_time = datetime.now(timezone.utc).isoformat()
        
        # 计算总执行时间
        if self.current_path.start_time:
            start = datetime.fromisoformat(self.current_path.start_time.replace('Z', '+00:00'))
            end = datetime.fromisoformat(self.current_path.end_time.replace('Z', '+00:00'))
            self.current_path.total_execution_time_ms = (end - start).total_seconds() * 1000
            
        # 生成调用树和性能指标
        self.current_path.call_tree = self._build_call_tree()
        self.current_path.performance_metrics = self._calculate_performance_metrics()
        
        self.logger.info(f"执行路径追踪完成: {self.current_path.path_id}")
        self.logger.info(f"  总函数调用: {len(self.current_path.function_calls)}")
        self.logger.info(f"  总执行时间: {self.current_path.total_execution_time_ms:.1f}ms")
        self.logger.info(f"  峰值内存: {self.peak_memory:.1f}MB")
        
        completed_path = self.current_path
        self.current_path = None
        
        return completed_path
        
    def _build_call_tree(self) -> Dict[str, Any]:
        """构建调用树"""
        if not self.current_path.function_calls or not self.current_path.function_exits:
            return {}
            
        # 创建调用ID到退出记录的映射
        exit_map = {exit.call_id: exit for exit in self.current_path.function_exits}
        
        # 构建树结构
        tree = {"root": {"children": [], "calls": []}}
        stack = [tree["root"]]
        
        for call in self.current_path.function_calls:
            # 获取对应的退出记录
            exit_record = exit_map.get(call.call_id)
            
            # 创建节点
            node = {
                "call_id": call.call_id,
                "function": f"{call.class_name}.{call.function_name}" if call.class_name else call.function_name,
                "module": call.module_name,
                "file": Path(call.file_path).name,
                "line": call.line_number,
                "timestamp": call.timestamp,
                "execution_time_ms": exit_record.execution_time_ms if exit_record else None,
                "memory_usage_mb": exit_record.memory_usage_mb if exit_record else None,
                "children": []
            }
            
            # 添加到当前层级
            if stack:
                stack[-1]["children"].append(node)
            
            # 如果有退出记录，说明这个函数已经完成，不需要压栈
            if not exit_record:
                stack.append(node)
            elif len(stack) > 1:
                # 函数完成，出栈
                stack.pop()
                
        return tree
        
    def _calculate_performance_metrics(self) -> Dict[str, Any]:
        """计算性能指标"""
        if not self.current_path.function_exits:
            return {}
            
        # 计算基本统计
        execution_times = [exit.execution_time_ms for exit in self.current_path.function_exits]
        memory_usages = [exit.memory_usage_mb for exit in self.current_path.function_exits]
        
        # 找出最慢的函数
        slow_functions = []
        for call, exit in zip(self.current_path.function_calls, self.current_path.function_exits):
            if exit.call_id == call.call_id:
                func_name = f"{call.class_name}.{call.function_name}" if call.class_name else call.function_name
                slow_functions.append((func_name, exit.execution_time_ms))
                
        slow_functions.sort(key=lambda x: x[1], reverse=True)
        
        # 计算异常数量
        exception_count = sum(1 for exit in self.current_path.function_exits if exit.exception)
        
        return {
            "total_functions_called": len(self.current_path.function_calls),
            "average_execution_time_ms": sum(execution_times) / len(execution_times) if execution_times else 0,
            "slowest_functions": slow_functions[:10],  # 前10个最慢的函数
            "memory_peak_mb": max(memory_usages) if memory_usages else 0,
            "cpu_peak_percent": max([exit.cpu_usage_percent for exit in self.current_path.function_exits]) if self.current_path.function_exits else 0,
            "exception_count": exception_count,
            "call_depth_max": max(len(self.call_stack), len(self.current_path.function_calls))
        }
        
    def save_execution_trace(self, execution_path: ExecutionPath) -> Path:
        """保存执行追踪结果"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        trace_file = self.output_dir / f"execution_trace_{self.scenario_id}_{timestamp}.json"
        
        # 转换为可序列化的格式
        trace_data = {
            "scenario_id": self.scenario_id,
            "path_id": execution_path.path_id,
            "start_time": execution_path.start_time,
            "end_time": execution_path.end_time,
            "total_execution_time_ms": execution_path.total_execution_time_ms,
            "function_calls": [asdict(call) for call in execution_path.function_calls],
            "function_exits": [asdict(exit) for exit in execution_path.function_exits],
            "call_tree": execution_path.call_tree,
            "performance_metrics": execution_path.performance_metrics,
            "summary": {
                "total_functions": len(execution_path.function_calls),
                "completed_functions": len(execution_path.function_exits),
                "avg_execution_time": execution_path.performance_metrics.get("average_execution_time_ms", 0),
                "peak_memory_mb": execution_path.performance_metrics.get("memory_peak_mb", 0),
                "exception_count": execution_path.performance_metrics.get("exception_count", 0)
            }
        }
        
        with trace_file.open('w', encoding='utf-8') as f:
            json.dump(trace_data, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"执行追踪结果已保存: {trace_file}")
        return trace_file


# 装饰器版本的追踪器
def trace_execution(scenario_id: str, output_dir: Path = None):
    """
    装饰器版本的执行追踪器
    
    使用方法:
    @trace_execution("scenario_1")
    async def my_function():
        pass
    """
    def decorator(func):
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            tracer = CodeExecutionTracer(scenario_id, output_dir)
            path_id = tracer.start_tracing(f"Function: {func.__name__}")
            
            try:
                result = await func(*args, **kwargs)
                return result
            finally:
                execution_path = tracer.stop_tracing()
                if execution_path:
                    tracer.save_execution_trace(execution_path)
                    
        @functools.wraps(func)  
        def sync_wrapper(*args, **kwargs):
            tracer = CodeExecutionTracer(scenario_id, output_dir)
            path_id = tracer.start_tracing(f"Function: {func.__name__}")
            
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                execution_path = tracer.stop_tracing()
                if execution_path:
                    tracer.save_execution_trace(execution_path)
                    
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
            
    return decorator


async def trace_magentic_one_execution(scenario_id: str, command: str, 
                                      output_dir: Path = None) -> Tuple[Any, Path]:
    """
    追踪Magentic-One的完整执行路径
    
    Args:
        scenario_id: 场景ID
        command: 要执行的命令
        output_dir: 输出目录
        
    Returns:
        Tuple[Any, Path]: (执行结果, 追踪文件路径)
    """
    from autogen_ext.models.ollama import OllamaChatCompletionClient
    from autogen_ext.teams.magentic_one import MagenticOne
    from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor
    
    tracer = CodeExecutionTracer(scenario_id, output_dir)
    
    try:
        # 开始追踪
        path_id = tracer.start_tracing(command)
        
        # 创建MagenticOne实例
        client = OllamaChatCompletionClient(
            model="llama3.1",
            host="http://localhost:11434"
        )
        code_executor = LocalCommandLineCodeExecutor()
        m1 = MagenticOne(client=client, code_executor=code_executor)
        
        # 执行任务
        results = []
        async for item in m1.run_stream(task=command):
            results.append({
                "type": type(item).__name__,
                "content": str(getattr(item, 'content', item)),
                "source": str(getattr(item, 'source', 'unknown')),
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        
        return results, None
        
    except Exception as e:
        tracer.logger.error(f"执行过程中出现错误: {e}")
        raise
    finally:
        # 停止追踪并保存结果
        execution_path = tracer.stop_tracing()
        if execution_path:
            trace_file = tracer.save_execution_trace(execution_path)
            return results if 'results' in locals() else None, trace_file
        return None, None


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Magentic-One 代码执行路径追踪器")
    parser.add_argument("--scenario", required=True, help="场景ID")
    parser.add_argument("--command", help="要追踪的命令 (如果不提供则从场景文件读取)")
    parser.add_argument("--output-dir", help="输出目录", default="debug")
    
    args = parser.parse_args()
    
    # 如果没有提供命令，从场景文件读取
    command = args.command
    if not command:
        scenario_file = Path(f"Agents_Failure_Attribution/Who&When/Hand-Crafted/{args.scenario}.json")
        if scenario_file.exists():
            with scenario_file.open(encoding='utf-8') as f:
                scenario = json.load(f)
            command = scenario["question"]
        else:
            print(f"错误: 场景文件不存在: {scenario_file}")
            sys.exit(1)
    
    print(f"🔍 启动代码执行路径追踪器...")
    print(f"📋 场景: {args.scenario}")
    print(f"💬 命令: {command}")
    print(f"📁 输出目录: {args.output_dir}")
    
    try:
        result, trace_file = asyncio.run(
            trace_magentic_one_execution(args.scenario, command, Path(args.output_dir))
        )
        
        if trace_file:
            print(f"\n✅ 执行路径追踪完成!")
            print(f"📄 追踪文件: {trace_file}")
            
            # 显示简要统计
            with trace_file.open('r', encoding='utf-8') as f:
                trace_data = json.load(f)
                
            summary = trace_data.get('summary', {})
            print(f"📊 总函数调用: {summary.get('total_functions', 0)}")
            print(f"⏱️  平均执行时间: {summary.get('avg_execution_time', 0):.1f}ms")
            print(f"💾 峰值内存: {summary.get('peak_memory_mb', 0):.1f}MB")
            print(f"❌ 异常数量: {summary.get('exception_count', 0)}")
        else:
            print(f"\n⚠️ 追踪文件未生成")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)