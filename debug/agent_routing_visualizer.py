#!/usr/bin/env python3
"""
Magentic-One 代理路由可视化器
===========================

这个模块专门分析和可视化Magentic-One系统中的代理选择和路由决策过程。
它能够：
1. 解析Orchestrator的决策逻辑
2. 追踪每次代理选择的原因和过程
3. 生成代理间通信的可视化图表
4. 分析代理选择模式和效率

作者: Claude Code
创建时间: 2025-07-31
"""

import json
import logging
import asyncio
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
import re
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as patches
from collections import defaultdict, Counter

@dataclass
class AgentRoutingEvent:
    """代理路由事件"""
    timestamp: str
    orchestrator_round: int
    decision_type: str  # 'initial_selection', 'reselection', 'handoff'
    selected_agent: str
    selection_reason: str
    context_facts: List[str]
    context_plan: List[str]
    progress_assessment: str
    alternatives_considered: List[str]
    confidence_score: Optional[float] = None

@dataclass 
class AgentCommunication:
    """代理通信记录"""
    timestamp: str
    from_agent: str
    to_agent: str
    message_type: str
    content_summary: str
    full_content: str
    message_id: str

@dataclass
class RoutingAnalysis:
    """路由分析结果"""
    total_routing_events: int
    agent_selection_frequency: Dict[str, int]
    routing_patterns: List[str]
    efficiency_metrics: Dict[str, float]
    communication_graph: Dict[str, Any]
    timeline: List[AgentRoutingEvent]

class AgentRoutingVisualizer:
    """代理路由可视化器"""
    
    def __init__(self, scenario_id: str, output_dir: Path = None):
        self.scenario_id = scenario_id
        self.output_dir = output_dir or Path("debug")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 数据存储
        self.routing_events: List[AgentRoutingEvent] = []
        self.communications: List[AgentCommunication] = []
        self.agent_metadata = {
            "Orchestrator": {
                "role": "决策协调者",
                "capabilities": ["任务分解", "进度评估", "代理选择", "结果整合"],
                "color": "#FF6B6B"
            },
            "WebSurfer": {
                "role": "网页浏览器",
                "capabilities": ["网页导航", "信息搜索", "表单填写", "屏幕截图"],
                "color": "#4ECDC4"
            },
            "FileSurfer": {
                "role": "文件浏览器", 
                "capabilities": ["文件读取", "目录浏览", "文本提取", "文档分析"],
                "color": "#45B7D1"
            },
            "Coder": {
                "role": "代码生成器",
                "capabilities": ["代码编写", "脚本生成", "数据分析", "算法实现"],
                "color": "#96CEB4"
            },
            "ComputerTerminal": {
                "role": "命令执行器",
                "capabilities": ["命令执行", "脚本运行", "系统操作", "文件管理"],
                "color": "#FFEAA7"
            }
        }
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志记录"""  
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        log_file = self.output_dir / f"agent_routing_analysis_{self.scenario_id}_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s [%(levelname)s] %(name)s: %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger("AgentRoutingVisualizer")
        self.logger.info(f"代理路由可视化器初始化 - 场景 {self.scenario_id}")
        
    def parse_llm_calls_for_routing(self, llm_calls_file: Path) -> None:
        """从LLM调用日志中解析路由决策"""
        self.logger.info(f"解析LLM调用日志: {llm_calls_file}")
        
        try:
            with llm_calls_file.open('r', encoding='utf-8') as f:
                data = json.load(f)
            
            llm_calls = data.get('llm_calls', [])
            orchestrator_round = 0
            
            for call in llm_calls:
                # 分析输入消息以确定调用类型
                input_messages = call.get('input_messages', [])
                if not input_messages:
                    continue
                    
                # 获取主要输入内容
                main_content = input_messages[-1].get('content', '')
                call_timestamp = call.get('timestamp', '')
                
                # 检查是否是Orchestrator的决策调用
                if self._is_orchestrator_decision_call(main_content):
                    orchestrator_round += 1
                    routing_event = self._extract_routing_decision(
                        main_content, call.get('response', {}), 
                        call_timestamp, orchestrator_round
                    )
                    if routing_event:
                        self.routing_events.append(routing_event)
                        
        except Exception as e:
            self.logger.error(f"解析LLM调用日志时出错: {e}")
            
    def parse_system_logs_for_communication(self, system_log_file: Path) -> None:
        """从系统日志中解析代理通信"""
        self.logger.info(f"解析系统日志: {system_log_file}")
        
        try:
            with system_log_file.open('r', encoding='utf-8') as f:
                log_data = json.load(f)
                
            for entry in log_data:
                content = entry.get('content', '')
                timestamp = entry.get('timestamp', '')
                
                # 查找代理通信模式
                if isinstance(content, dict):
                    # 消息传递事件
                    if content.get('type') == 'Message':
                        comm = self._extract_communication_from_message(content, timestamp)
                        if comm:
                            self.communications.append(comm)
                elif isinstance(content, str):
                    # 文本日志中的通信信息
                    comm = self._extract_communication_from_text(content, timestamp)
                    if comm:
                        self.communications.append(comm)
                        
        except Exception as e:
            self.logger.error(f"解析系统日志时出错: {e}")
            
    def _is_orchestrator_decision_call(self, content: str) -> bool:
        """判断是否是Orchestrator决策调用"""
        decision_indicators = [
            "progress_ledger",
            "task_ledger",
            "which agent should act next",
            "select the most appropriate agent",
            "agent selection",
            "orchestration"
        ]
        content_lower = content.lower()
        return any(indicator in content_lower for indicator in decision_indicators)
        
    def _extract_routing_decision(self, input_content: str, response: Dict, 
                                 timestamp: str, round_num: int) -> Optional[AgentRoutingEvent]:
        """从LLM调用中提取路由决策信息"""
        try:
            # 提取响应内容
            response_content = ""
            if response.get('message', {}).get('content'):
                response_content = response['message']['content']
            elif response.get('content'):
                response_content = response['content']
                
            # 解析代理选择
            selected_agent = self._extract_selected_agent(response_content)
            if not selected_agent:
                return None
                
            # 提取决策原因
            selection_reason = self._extract_selection_reason(response_content)
            
            # 提取上下文信息
            context_facts = self._extract_context_facts(input_content)
            context_plan = self._extract_context_plan(input_content)
            progress_assessment = self._extract_progress_assessment(input_content)
            
            # 确定决策类型
            decision_type = self._determine_decision_type(input_content)
            
            return AgentRoutingEvent(
                timestamp=timestamp,
                orchestrator_round=round_num,
                decision_type=decision_type,
                selected_agent=selected_agent,
                selection_reason=selection_reason,
                context_facts=context_facts,
                context_plan=context_plan,
                progress_assessment=progress_assessment,
                alternatives_considered=self._extract_alternatives(response_content)
            )
            
        except Exception as e:
            self.logger.warning(f"提取路由决策时出错: {e}")
            return None
            
    def _extract_selected_agent(self, content: str) -> Optional[str]:
        """提取选中的代理"""
        # 查找JSON格式的响应
        json_match = re.search(r'\{[^}]*"agent"[^}]*\}', content, re.IGNORECASE)
        if json_match:
            try:
                json_obj = json.loads(json_match.group())
                return json_obj.get('agent', '')
            except:
                pass
                
        # 查找明确的代理名称
        agent_patterns = [
            r"selected?\s+agent[:\s]+(\w+)",
            r"agent[:\s]+(\w+)",
            r"next\s+agent[:\s]+(\w+)",
            r"(\w*surfer|\w*coder|\w*terminal)",
        ]
        
        for pattern in agent_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                agent_name = match.group(1).strip()
                # 标准化代理名称
                return self._normalize_agent_name(agent_name)
                
        return None
        
    def _normalize_agent_name(self, name: str) -> str:
        """标准化代理名称"""
        name_lower = name.lower()
        if 'websurfer' in name_lower or 'web' in name_lower:
            return 'WebSurfer'
        elif 'filesurfer' in name_lower or 'file' in name_lower:
            return 'FileSurfer'
        elif 'coder' in name_lower:
            return 'Coder'
        elif 'terminal' in name_lower or 'computer' in name_lower:
            return 'ComputerTerminal'
        elif 'orchestrator' in name_lower:
            return 'Orchestrator'
        else:
            return name.title()
            
    def _extract_selection_reason(self, content: str) -> str:
        """提取选择原因"""
        reason_patterns = [
            r"reasoning?[:\s]+([^.!?]*[.!?])",
            r"because[:\s]+([^.!?]*[.!?])",
            r"rationale[:\s]+([^.!?]*[.!?])",
            r"explanation[:\s]+([^.!?]*[.!?])"
        ]
        
        for pattern in reason_patterns:
            match = re.search(pattern, content, re.IGNORECASE | re.DOTALL)
            if match:
                return match.group(1).strip()
                
        # 尝试提取JSON中的reasoning字段
        json_match = re.search(r'\{[^}]*"reasoning"[^}]*\}', content, re.IGNORECASE)
        if json_match:
            try:
                json_obj = json.loads(json_match.group())
                return json_obj.get('reasoning', '')
            except:
                pass
                
        return "未找到明确的选择原因"
        
    def _extract_context_facts(self, content: str) -> List[str]:
        """提取上下文事实"""
        facts = []
        
        # 查找事实部分
        facts_section = re.search(r"task_ledger_facts[:\s]*([^}]*)", content, re.IGNORECASE | re.DOTALL)
        if facts_section:
            facts_text = facts_section.group(1)
            # 提取列表项
            fact_items = re.findall(r"[-*]\s*([^-*\n]+)", facts_text)
            facts.extend([fact.strip() for fact in fact_items])
            
        return facts[:5]  # 限制数量
        
    def _extract_context_plan(self, content: str) -> List[str]:
        """提取上下文计划"""
        plan = []
        
        # 查找计划部分
        plan_section = re.search(r"task_ledger_plan[:\s]*([^}]*)", content, re.IGNORECASE | re.DOTALL)
        if plan_section:
            plan_text = plan_section.group(1)
            # 提取步骤
            plan_items = re.findall(r"[-*]\s*([^-*\n]+)", plan_text)
            plan.extend([step.strip() for step in plan_items])
            
        return plan[:5]  # 限制数量
        
    def _extract_progress_assessment(self, content: str) -> str:
        """提取进度评估"""
        progress_patterns = [
            r"progress[:\s]+([^.!?\n]*[.!?])",
            r"is_progress_being_made[:\s]*([^,}\n]*)",
            r"assessment[:\s]+([^.!?\n]*[.!?])"
        ]
        
        for pattern in progress_patterns:
            match = re.search(pattern, content, re.IGNORECASE)
            if match:
                return match.group(1).strip()
                
        return "无明确进度评估"
        
    def _determine_decision_type(self, content: str) -> str:
        """确定决策类型"""
        content_lower = content.lower() 
        
        if "rethink" in content_lower or "re-think" in content_lower:
            return "rethink_decision"
        elif "handoff" in content_lower or "hand-off" in content_lower:
            return "handoff"
        elif "reselect" in content_lower or "re-select" in content_lower:
            return "reselection"
        else:
            return "initial_selection"
            
    def _extract_alternatives(self, content: str) -> List[str]:
        """提取考虑的替代方案"""
        alternatives = []
        
        # 查找提到的其他代理
        for agent in self.agent_metadata.keys():
            if agent.lower() in content.lower() and agent != "Orchestrator":
                alternatives.append(agent)
                
        return alternatives
        
    def _extract_communication_from_message(self, message_data: Dict, timestamp: str) -> Optional[AgentCommunication]:
        """从消息数据中提取通信信息"""
        try:
            receiver = message_data.get('receiver', '')
            payload = message_data.get('payload', '')
            
            # 解析接收者
            to_agent = self._extract_agent_from_id(receiver)
            if not to_agent:
                return None
                
            # 解析payload以获取内容
            content_summary = payload[:100] + "..." if len(payload) > 100 else payload
            
            return AgentCommunication(
                timestamp=timestamp,
                from_agent="Unknown",  # 发送者信息可能需要从上下文推断
                to_agent=to_agent,
                message_type="DirectMessage",
                content_summary=content_summary,
                full_content=payload,
                message_id=message_data.get('delivery_stage', '')
            )
            
        except Exception as e:
            self.logger.warning(f"提取消息通信时出错: {e}")
            return None
            
    def _extract_communication_from_text(self, text: str, timestamp: str) -> Optional[AgentCommunication]:
        """从文本日志中提取通信信息"""
        # 查找类似 "Sending message of type X to Y" 的模式
        send_pattern = r"Sending message of type (\w+) to (\w+[^:]*):?\s*(.+)"
        match = re.search(send_pattern, text)
        
        if match:
            message_type = match.group(1)
            to_agent = self._extract_agent_from_id(match.group(2))
            content = match.group(3)
            
            if to_agent:
                return AgentCommunication(
                    timestamp=timestamp,
                    from_agent="System",
                    to_agent=to_agent,
                    message_type=message_type,
                    content_summary=content[:100] + "..." if len(content) > 100 else content,
                    full_content=content,
                    message_id=""
                )
                
        return None
        
    def _extract_agent_from_id(self, agent_id: str) -> Optional[str]:
        """从代理ID中提取代理名称"""
        for agent_name in self.agent_metadata.keys():
            if agent_name.lower() in agent_id.lower():
                return agent_name
        return None
        
    def analyze_routing_patterns(self) -> RoutingAnalysis:
        """分析路由模式"""
        self.logger.info("分析路由模式...")
        
        # 统计代理选择频率
        agent_frequency = Counter()
        for event in self.routing_events:
            agent_frequency[event.selected_agent] += 1
            
        # 分析路由模式
        patterns = []
        if len(self.routing_events) >= 2:
            for i in range(len(self.routing_events) - 1):
                pattern = f"{self.routing_events[i].selected_agent} -> {self.routing_events[i+1].selected_agent}"
                patterns.append(pattern)
                
        # 计算效率指标
        efficiency_metrics = {
            "avg_round_per_decision": len(self.routing_events) / max(1, len(set(e.selected_agent for e in self.routing_events))),
            "decision_diversity": len(set(e.selected_agent for e in self.routing_events)) / max(1, len(self.routing_events)),
            "rethink_frequency": len([e for e in self.routing_events if e.decision_type == "rethink_decision"]) / max(1, len(self.routing_events))
        }
        
        # 构建通信图
        comm_graph = self._build_communication_graph()
        
        return RoutingAnalysis(
            total_routing_events=len(self.routing_events),
            agent_selection_frequency=dict(agent_frequency),
            routing_patterns=list(set(patterns)),
            efficiency_metrics=efficiency_metrics,
            communication_graph=comm_graph,
            timeline=self.routing_events
        )
        
    def _build_communication_graph(self) -> Dict[str, Any]:
        """构建通信图"""
        # 创建有向图
        G = nx.DiGraph()
        
        # 添加所有代理作为节点
        for agent in self.agent_metadata.keys():
            G.add_node(agent, **self.agent_metadata[agent])
            
        # 添加路由决策边
        for event in self.routing_events:
            # Orchestrator到选中代理的边
            if event.selected_agent != "Orchestrator":
                G.add_edge("Orchestrator", event.selected_agent, 
                          type="routing_decision", 
                          reason=event.selection_reason)
                          
        # 添加通信边
        for comm in self.communications:
            if comm.from_agent != comm.to_agent:
                if G.has_edge(comm.from_agent, comm.to_agent):
                    G[comm.from_agent][comm.to_agent]['weight'] = G[comm.from_agent][comm.to_agent].get('weight', 0) + 1
                else:
                    G.add_edge(comm.from_agent, comm.to_agent, 
                              type="communication", weight=1)
                              
        # 转换为可序列化的格式
        return {
            "nodes": [{"id": node, **G.nodes[node]} for node in G.nodes()],
            "edges": [{"from": u, "to": v, **G[u][v]} for u, v in G.edges()]
        }
        
    def generate_routing_visualization(self, analysis: RoutingAnalysis) -> Path:
        """生成路由可视化图表"""
        self.logger.info("生成路由可视化图表...")
        
        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle(f'Magentic-One 代理路由分析 - 场景 {self.scenario_id}', fontsize=16, fontweight='bold')
        
        # 1. 代理选择频率饼图
        if analysis.agent_selection_frequency:
            agents = list(analysis.agent_selection_frequency.keys())
            frequencies = list(analysis.agent_selection_frequency.values())
            colors = [self.agent_metadata.get(agent, {}).get('color', '#CCCCCC') for agent in agents]
            
            ax1.pie(frequencies, labels=agents, colors=colors, autopct='%1.1f%%', startangle=90)
            ax1.set_title('代理选择频率分布')
        else:
            ax1.text(0.5, 0.5, '无代理选择数据', ha='center', va='center', transform=ax1.transAxes)
            ax1.set_title('代理选择频率分布')
            
        # 2. 路由时间线
        if self.routing_events:
            events_by_round = [(e.orchestrator_round, e.selected_agent) for e in self.routing_events]
            rounds, agents = zip(*events_by_round)
            
            # 为每个代理分配y坐标
            unique_agents = list(set(agents))
            agent_positions = {agent: i for i, agent in enumerate(unique_agents)}
            y_positions = [agent_positions[agent] for agent in agents]
            
            colors = [self.agent_metadata.get(agent, {}).get('color', '#CCCCCC') for agent in agents]
            ax2.scatter(rounds, y_positions, c=colors, s=100, alpha=0.7)
            
            # 连线显示路由路径
            for i in range(len(rounds) - 1):
                ax2.plot([rounds[i], rounds[i+1]], [y_positions[i], y_positions[i+1]], 
                        'k--', alpha=0.3, linewidth=1)
                        
            ax2.set_xlabel('Orchestrator 轮次')
            ax2.set_ylabel('选中的代理')
            ax2.set_yticks(range(len(unique_agents)))
            ax2.set_yticklabels(unique_agents)
            ax2.set_title('代理选择时间线')
            ax2.grid(True, alpha=0.3)
        else:
            ax2.text(0.5, 0.5, '无路由事件数据', ha='center', va='center', transform=ax2.transAxes)
            ax2.set_title('代理选择时间线')
            
        # 3. 效率指标柱状图
        if analysis.efficiency_metrics:
            metrics = list(analysis.efficiency_metrics.keys())
            values = list(analysis.efficiency_metrics.values())
            
            bars = ax3.bar(range(len(metrics)), values, color=['#FF9999', '#66B2FF', '#99FF99'])
            ax3.set_xlabel('效率指标')
            ax3.set_ylabel('数值')
            ax3.set_title('路由效率指标')
            ax3.set_xticks(range(len(metrics)))
            ax3.set_xticklabels([m.replace('_', '\n') for m in metrics], rotation=45, ha='right')
            
            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                        f'{value:.3f}', ha='center', va='bottom')
        else:
            ax3.text(0.5, 0.5, '无效率指标数据', ha='center', va='center', transform=ax3.transAxes)
            ax3.set_title('路由效率指标')
            
        # 4. 路由模式网络图
        if analysis.communication_graph and analysis.communication_graph['nodes']:
            # 使用NetworkX绘制网络图
            G = nx.DiGraph()
            for node in analysis.communication_graph['nodes']:
                G.add_node(node['id'])
            for edge in analysis.communication_graph['edges']:
                G.add_edge(edge['from'], edge['to'])
                
            pos = nx.spring_layout(G, k=1, iterations=50)
            
            # 绘制节点
            node_colors = []
            for node in G.nodes():
                color = self.agent_metadata.get(node, {}).get('color', '#CCCCCC')
                node_colors.append(color)
                
            nx.draw_networkx_nodes(G, pos, node_color=node_colors, 
                                 node_size=1000, alpha=0.8, ax=ax4)
            nx.draw_networkx_labels(G, pos, font_size=8, ax=ax4)
            nx.draw_networkx_edges(G, pos, alpha=0.5, arrows=True, 
                                 arrow_size=20, ax=ax4)
                                 
            ax4.set_title('代理通信网络')
            ax4.axis('off')
        else:
            ax4.text(0.5, 0.5, '无网络数据', ha='center', va='center', transform=ax4.transAxes)
            ax4.set_title('代理通信网络')
            
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        chart_file = self.output_dir / f"agent_routing_visualization_{self.scenario_id}_{timestamp}.png"
        plt.savefig(chart_file, dpi=300, bbox_inches='tight')
        plt.close()
        
        self.logger.info(f"路由可视化图表已保存: {chart_file}")
        return chart_file
        
    def generate_routing_report(self, analysis: RoutingAnalysis) -> Path:
        """生成详细的路由分析报告"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        report_file = self.output_dir / f"agent_routing_report_{self.scenario_id}_{timestamp}.json"
        
        report = {
            "scenario_id": self.scenario_id,
            "generation_time": datetime.now(timezone.utc).isoformat(),
            "analysis": asdict(analysis),
            "agent_metadata": self.agent_metadata,
            "detailed_events": [asdict(event) for event in self.routing_events],
            "communications": [asdict(comm) for comm in self.communications],
            "insights": self._generate_insights(analysis)
        }
        
        with report_file.open('w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"路由分析报告已保存: {report_file}")
        return report_file
        
    def _generate_insights(self, analysis: RoutingAnalysis) -> Dict[str, Any]:
        """生成分析洞察"""
        insights = {
            "most_selected_agent": max(analysis.agent_selection_frequency.items(), key=lambda x: x[1]) if analysis.agent_selection_frequency else ("None", 0),
            "routing_efficiency": "高" if analysis.efficiency_metrics.get("decision_diversity", 0) > 0.5 else "中" if analysis.efficiency_metrics.get("decision_diversity", 0) > 0.3 else "低",
            "rethink_tendency": "频繁" if analysis.efficiency_metrics.get("rethink_frequency", 0) > 0.2 else "偶尔" if analysis.efficiency_metrics.get("rethink_frequency", 0) > 0.1 else "很少",
            "common_patterns": analysis.routing_patterns[:5] if analysis.routing_patterns else [],
            "total_communications": len(self.communications)
        }
        
        return insights


async def analyze_agent_routing(scenario_id: str, 
                               llm_calls_file: Path,
                               system_log_file: Path,
                               output_dir: Path = None) -> Tuple[RoutingAnalysis, Path, Path]:
    """
    分析代理路由模式
    
    Args:
        scenario_id: 场景ID
        llm_calls_file: LLM调用日志文件
        system_log_file: 系统日志文件
        output_dir: 输出目录
        
    Returns:
        Tuple[RoutingAnalysis, Path, Path]: (分析结果, 可视化文件, 报告文件)
    """
    visualizer = AgentRoutingVisualizer(scenario_id, output_dir)
    
    # 解析日志文件
    if llm_calls_file.exists():
        visualizer.parse_llm_calls_for_routing(llm_calls_file)
    else:
        visualizer.logger.warning(f"LLM调用日志文件不存在: {llm_calls_file}")
        
    if system_log_file.exists():
        visualizer.parse_system_logs_for_communication(system_log_file)
    else:
        visualizer.logger.warning(f"系统日志文件不存在: {system_log_file}")
    
    # 进行分析
    analysis = visualizer.analyze_routing_patterns()
    
    # 生成可视化和报告
    viz_file = visualizer.generate_routing_visualization(analysis)
    report_file = visualizer.generate_routing_report(analysis)
    
    return analysis, viz_file, report_file


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Magentic-One 代理路由可视化器")
    parser.add_argument("--scenario", required=True, help="场景ID")
    parser.add_argument("--llm-calls", help="LLM调用日志文件路径")
    parser.add_argument("--system-log", help="系统日志文件路径")
    parser.add_argument("--output-dir", help="输出目录", default="debug")
    
    args = parser.parse_args()
    
    # 自动查找日志文件
    if not args.llm_calls or not args.system_log:
        logs_dir = Path("logs/generated")
        if logs_dir.exists():
            # 查找最新的日志文件
            llm_files = list(logs_dir.glob(f"*scenario_{args.scenario}*_llm_calls_formatted.json"))
            log_files = list(logs_dir.glob(f"*scenario_{args.scenario}*_log_formatted.json"))
            
            if llm_files and not args.llm_calls:
                args.llm_calls = str(sorted(llm_files)[-1])  # 最新的文件
            if log_files and not args.system_log:
                args.system_log = str(sorted(log_files)[-1])  # 最新的文件
    
    if not args.llm_calls or not args.system_log:
        print("错误: 请提供LLM调用日志和系统日志文件路径，或确保logs/generated目录中存在相应文件")
        sys.exit(1)
    
    print(f"🎯 启动代理路由可视化器...")
    print(f"📋 场景: {args.scenario}")
    print(f"📊 LLM调用日志: {args.llm_calls}")
    print(f"📋 系统日志: {args.system_log}")
    print(f"📁 输出目录: {args.output_dir}")
    
    try:
        analysis, viz_file, report_file = asyncio.run(
            analyze_agent_routing(
                args.scenario,
                Path(args.llm_calls),
                Path(args.system_log),
                Path(args.output_dir)
            )
        )
        
        print(f"\n✅ 路由分析完成!")
        print(f"📊 总路由事件: {analysis.total_routing_events}")
        print(f"🔀 路由模式: {len(analysis.routing_patterns)}")
        print(f"📈 可视化图表: {viz_file}")
        print(f"📄 详细报告: {report_file}")
        
        # 显示关键洞察
        if analysis.agent_selection_frequency:
            most_used = max(analysis.agent_selection_frequency.items(), key=lambda x: x[1])
            print(f"🏆 最常用代理: {most_used[0]} ({most_used[1]}次)")
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
    except Exception as e:
        print(f"\n❌ 分析失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)