#!/usr/bin/env python3
"""
Magentic-One 命令处理流程追踪器
==============================

这个模块提供了对Magentic-One系统完整命令处理流程的深度追踪功能。
它能够：
1. 追踪输入命令如何被转换为prompts
2. 监控代理选择决策过程  
3. 记录每个函数调用的完整调用栈
4. 生成详细的执行流程映射

作者: Claude Code
创建时间: 2025-07-31
"""

import asyncio
import json
import logging
import inspect
import traceback
import functools
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple, Union
from dataclasses import dataclass, asdict
import hashlib
import sys
import os

# AutoGen 相关导入
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor

# 数据结构定义
@dataclass
class SourceLocation:
    """源码位置信息"""
    file_path: str
    line_number: int
    function_name: str
    class_name: Optional[str] = None
    
    def __str__(self):
        if self.class_name:
            return f"{self.class_name}.{self.function_name}() at {Path(self.file_path).name}:{self.line_number}"
        else:
            return f"{self.function_name}() at {Path(self.file_path).name}:{self.line_number}"

@dataclass 
class FlowEvent:
    """流程事件"""
    event_id: str
    timestamp: str
    event_type: str  # 'command_input', 'prompt_generation', 'agent_selection', 'function_call', 'response_generation'
    stage: str       # 'input', 'processing', 'routing', 'execution', 'output'
    source_location: SourceLocation
    data: Dict[str, Any]
    parent_event_id: Optional[str] = None
    call_stack: List[SourceLocation] = None
    execution_time_ms: Optional[float] = None
    
    def __post_init__(self):
        if self.call_stack is None:
            self.call_stack = []

@dataclass
class CommandTrace:
    """完整的命令追踪记录"""
    command_id: str
    original_command: str
    start_time: str
    end_time: Optional[str] = None
    events: List[FlowEvent] = None
    final_result: Optional[Dict[str, Any]] = None
    total_execution_time_ms: Optional[float] = None
    
    def __post_init__(self):
        if self.events is None:
            self.events = []

class CommandFlowTracer:
    """命令流程追踪器"""
    
    def __init__(self, scenario_id: str, output_dir: Path = None):
        self.scenario_id = scenario_id
        self.output_dir = output_dir or Path("debug")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # 追踪状态
        self.current_command: Optional[CommandTrace] = None
        self.completed_commands: List[CommandTrace] = []
        self.event_counter = 0
        self.function_hooks: Dict[str, Any] = {}
        
        # 设置日志记录
        self.setup_logging()
        
        # 源码映射缓存
        self.source_map_cache: Dict[str, Dict[str, Any]] = {}
        
    def setup_logging(self):
        """设置日志记录"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        log_file = self.output_dir / f"command_flow_trace_{self.scenario_id}_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s.%(msecs)03d [%(levelname)s] %(name)s: %(message)s',
            datefmt='%H:%M:%S',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger("CommandFlowTracer")
        self.logger.info(f"命令流程追踪器初始化完成 - 场景 {self.scenario_id}")
        
    def generate_event_id(self) -> str:
        """生成唯一的事件ID"""
        self.event_counter += 1
        return f"evt_{self.event_counter:04d}_{datetime.now(timezone.utc).strftime('%H%M%S%f')}"
        
    def get_current_source_location(self, skip_frames: int = 2) -> SourceLocation:
        """获取当前源码位置"""
        frame = inspect.currentframe()
        try:
            # 跳过指定数量的帧
            for _ in range(skip_frames):
                if frame.f_back:
                    frame = frame.f_back
                    
            if frame:
                filename = frame.f_code.co_filename
                line_number = frame.f_lineno
                function_name = frame.f_code.co_name
                
                # 尝试获取类名
                class_name = None
                if 'self' in frame.f_locals:
                    class_name = frame.f_locals['self'].__class__.__name__
                    
                return SourceLocation(
                    file_path=filename,
                    line_number=line_number,
                    function_name=function_name,
                    class_name=class_name
                )
        finally:
            del frame
            
        # 回退选项
        return SourceLocation(
            file_path="<unknown>",
            line_number=0,
            function_name="<unknown>"
        )
        
    def get_call_stack(self, max_depth: int = 10) -> List[SourceLocation]:
        """获取调用栈"""
        call_stack = []
        frame = inspect.currentframe()
        
        try:
            # 跳过当前帧
            if frame.f_back:
                frame = frame.f_back
                
            depth = 0
            while frame and depth < max_depth:
                filename = frame.f_code.co_filename
                line_number = frame.f_lineno
                function_name = frame.f_code.co_name
                
                # 跳过内部框架代码
                if not any(skip in filename for skip in ['site-packages', 'python3.', '_pydev']):
                    class_name = None
                    if 'self' in frame.f_locals:
                        try:
                            class_name = frame.f_locals['self'].__class__.__name__
                        except:
                            pass
                            
                    call_stack.append(SourceLocation(
                        file_path=filename,
                        line_number=line_number,
                        function_name=function_name,
                        class_name=class_name
                    ))
                    
                frame = frame.f_back
                depth += 1
                
        finally:
            del frame
            
        return call_stack
        
    def start_command_trace(self, command: str) -> str:
        """开始命令追踪"""
        command_id = hashlib.md5(f"{command}_{datetime.now(timezone.utc).isoformat()}".encode()).hexdigest()[:12]
        
        self.current_command = CommandTrace(
            command_id=command_id,
            original_command=command,
            start_time=datetime.now(timezone.utc).isoformat()
        )
        
        # 记录命令输入事件
        self.log_event(
            event_type="command_input",
            stage="input",
            data={
                "command": command,
                "command_length": len(command),
                "summary": f"开始处理命令: {command[:100]}..."
            }
        )
        
        self.logger.info(f"开始追踪命令: {command_id} - {command[:50]}...")
        return command_id
        
    def log_event(self, event_type: str, stage: str, data: Dict[str, Any], 
                  parent_event_id: str = None, execution_time_ms: float = None):
        """记录流程事件"""
        if not self.current_command:
            self.logger.warning("尝试记录事件但没有活跃的命令追踪")
            return
            
        event = FlowEvent(
            event_id=self.generate_event_id(),
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type=event_type,
            stage=stage,
            source_location=self.get_current_source_location(skip_frames=3),
            data=data,
            parent_event_id=parent_event_id,
            call_stack=self.get_call_stack(),
            execution_time_ms=execution_time_ms
        )
        
        self.current_command.events.append(event)
        
        # 实时日志输出
        location_str = str(event.source_location)
        summary = data.get('summary', str(data)[:100])
        self.logger.info(f"[{event_type}:{stage}] {location_str} - {summary}")
        
    def create_instrumented_client(self, original_client):
        """创建带追踪功能的模型客户端"""
        tracer = self
        
        class InstrumentedClient:
            def __init__(self, original):
                self._original = original
                # 复制原始客户端的所有属性
                for attr in dir(original):
                    if not attr.startswith('_') and attr != 'create':
                        setattr(self, attr, getattr(original, attr))
            
            async def create(self, messages, **kwargs):
                """追踪LLM调用"""
                start_time = datetime.now()
                
                # 分析prompt内容以确定调用类型
                prompt_content = ""
                if messages:
                    prompt_content = str(messages[-1].content if hasattr(messages[-1], 'content') else messages[-1])
                
                call_type = self._analyze_llm_call_type(prompt_content)
                
                # 记录LLM调用开始
                tracer.log_event(
                    event_type="llm_call",
                    stage="processing",
                    data={
                        "call_type": call_type,
                        "num_messages": len(messages),
                        "total_chars": sum(len(str(getattr(msg, 'content', msg))) for msg in messages),
                        "kwargs": {k: str(v) for k, v in kwargs.items()},
                        "summary": f"LLM调用开始 - 类型: {call_type}, {len(messages)}条消息"
                    }
                )
                
                try:
                    # 执行原始调用
                    response = await self._original.create(messages, **kwargs)
                    end_time = datetime.now()
                    execution_time = (end_time - start_time).total_seconds() * 1000
                    
                    # 记录响应
                    tracer.log_event(
                        event_type="llm_response",
                        stage="processing", 
                        data={
                            "call_type": call_type,
                            "response_length": len(str(response.content)) if hasattr(response, 'content') else 0,
                            "response_preview": str(response.content)[:200] + "..." if hasattr(response, 'content') and len(str(response.content)) > 200 else str(getattr(response, 'content', response)),
                            "summary": f"LLM响应完成 - 类型: {call_type}, 耗时: {execution_time:.1f}ms"
                        },
                        execution_time_ms=execution_time
                    )
                    
                    return response
                    
                except Exception as e:
                    end_time = datetime.now()
                    execution_time = (end_time - start_time).total_seconds() * 1000
                    
                    tracer.log_event(
                        event_type="llm_error",
                        stage="processing",
                        data={
                            "call_type": call_type,
                            "error": str(e),
                            "traceback": traceback.format_exc(),
                            "summary": f"LLM调用失败 - 类型: {call_type}, 错误: {str(e)}"
                        },
                        execution_time_ms=execution_time
                    )
                    raise
            
            def _analyze_llm_call_type(self, prompt_content: str) -> str:
                """分析LLM调用类型"""
                content_lower = prompt_content.lower()
                
                if "task_ledger_facts" in content_lower:
                    return "task_facts_collection"
                elif "task_ledger_plan" in content_lower:
                    return "task_plan_generation"
                elif "progress_ledger" in content_lower:
                    return "progress_evaluation"
                elif "facts_update" in content_lower:
                    return "facts_update"
                elif "plan_update" in content_lower:
                    return "plan_update"
                elif "final_answer" in content_lower:
                    return "final_answer_generation"
                elif "pre-survey" in content_lower:
                    return "initial_analysis"
                else:
                    return "general_llm_call"
        
        return InstrumentedClient(original_client)
        
    def instrument_magentic_one(self, m1_instance):
        """为MagenticOne实例添加追踪功能"""
        # 这里需要根据实际的MagenticOne实现来添加hook
        # 由于MagenticOne的内部结构比较复杂，我们采用动态hook的方式
        
        # Hook run_stream方法
        original_run_stream = m1_instance.run_stream
        
        async def traced_run_stream(task, **kwargs):
            """追踪版本的run_stream"""
            self.log_event(
                event_type="task_start",
                stage="input",
                data={
                    "task": task,
                    "kwargs": {k: str(v) for k, v in kwargs.items()},
                    "summary": f"开始执行任务: {task[:100]}..."
                }
            )
            
            try:
                async for item in original_run_stream(task, **kwargs):
                    # 记录每个生成的项目
                    item_type = type(item).__name__
                    content = str(getattr(item, 'content', item))
                    source = str(getattr(item, 'source', 'unknown'))
                    
                    self.log_event(
                        event_type="agent_message",
                        stage="output",
                        data={
                            "item_type": item_type,
                            "source": source,
                            "content_length": len(content),
                            "content_preview": content[:300] + "..." if len(content) > 300 else content,
                            "summary": f"代理消息 - 来源: {source}, 类型: {item_type}"
                        }
                    )
                    
                    yield item
                    
            except Exception as e:
                self.log_event(
                    event_type="task_error",
                    stage="execution",
                    data={
                        "error": str(e),
                        "traceback": traceback.format_exc(),
                        "summary": f"任务执行出错: {str(e)}"
                    }
                )
                raise
        
        # 替换方法
        m1_instance._original_run_stream = original_run_stream
        m1_instance.run_stream = traced_run_stream
        
        return m1_instance
        
    def end_command_trace(self, final_result: Dict[str, Any] = None):
        """结束命令追踪"""
        if not self.current_command:
            self.logger.warning("尝试结束命令追踪但没有活跃的命令")
            return
            
        self.current_command.end_time = datetime.now(timezone.utc).isoformat()
        
        # 计算总执行时间
        if self.current_command.start_time:
            start = datetime.fromisoformat(self.current_command.start_time.replace('Z', '+00:00'))
            end = datetime.fromisoformat(self.current_command.end_time.replace('Z', '+00:00'))
            self.current_command.total_execution_time_ms = (end - start).total_seconds() * 1000
            
        if final_result:
            self.current_command.final_result = final_result
            
        # 记录命令完成事件
        self.log_event(
            event_type="command_complete",
            stage="output",
            data={
                "total_events": len(self.current_command.events),
                "execution_time_ms": self.current_command.total_execution_time_ms,
                "final_result": final_result,
                "summary": f"命令执行完成 - 共{len(self.current_command.events)}个事件"
            }
        )
        
        # 移动到已完成列表
        self.completed_commands.append(self.current_command)
        self.logger.info(f"命令追踪完成: {self.current_command.command_id}")
        self.current_command = None
        
    def generate_flow_report(self) -> Dict[str, Any]:
        """生成流程报告"""
        all_commands = self.completed_commands.copy()
        if self.current_command:
            all_commands.append(self.current_command)
            
        # 统计信息
        total_events = sum(len(cmd.events) for cmd in all_commands)
        event_type_counts = {}
        stage_counts = {}
        
        for cmd in all_commands:
            for event in cmd.events:
                event_type_counts[event.event_type] = event_type_counts.get(event.event_type, 0) + 1
                stage_counts[event.stage] = stage_counts.get(event.stage, 0) + 1
        
        # 生成详细报告
        report = {
            "scenario_id": self.scenario_id,
            "generation_time": datetime.now(timezone.utc).isoformat(),
            "summary": {
                "total_commands": len(all_commands),
                "total_events": total_events,
                "event_type_distribution": event_type_counts,
                "stage_distribution": stage_counts
            },
            "commands": []
        }
        
        for cmd in all_commands:
            cmd_data = asdict(cmd)
            # 转换SourceLocation对象为字典
            for event in cmd_data['events']:
                if event['source_location']:
                    event['source_location'] = asdict(event['source_location'])
                if event['call_stack']:
                    event['call_stack'] = [asdict(loc) for loc in event['call_stack']]
            report["commands"].append(cmd_data)
            
        return report
        
    def save_flow_report(self) -> Path:
        """保存流程报告"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        report_file = self.output_dir / f"command_flow_report_{self.scenario_id}_{timestamp}.json"
        
        report = self.generate_flow_report()
        
        with report_file.open('w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        self.logger.info(f"流程报告已保存到: {report_file}")
        return report_file


async def trace_command_execution(scenario_id: str, command: str, 
                                 output_dir: Path = None) -> Tuple[Dict[str, Any], Path]:
    """
    追踪单个命令的完整执行流程
    
    Args:
        scenario_id: 场景ID
        command: 要执行的命令
        output_dir: 输出目录
        
    Returns:
        Tuple[Dict[str, Any], Path]: (执行结果, 报告文件路径)
    """
    tracer = CommandFlowTracer(scenario_id, output_dir)
    
    try:
        # 开始命令追踪
        command_id = tracer.start_command_trace(command)
        
        # 创建带追踪功能的客户端
        original_client = OllamaChatCompletionClient(
            model="llama3.1",
            host="http://localhost:11434"
        )
        instrumented_client = tracer.create_instrumented_client(original_client)
        
        # 创建代码执行器
        code_executor = LocalCommandLineCodeExecutor()
        
        # 创建MagenticOne实例并添加追踪
        m1 = MagenticOne(client=instrumented_client, code_executor=code_executor)
        m1 = tracer.instrument_magentic_one(m1)
        
        # 执行任务并收集结果
        results = []
        async for item in m1.run_stream(task=command):
            results.append({
                "type": type(item).__name__,
                "content": str(getattr(item, 'content', item)),
                "source": str(getattr(item, 'source', 'unknown')),
                "timestamp": datetime.now(timezone.utc).isoformat()
            })
        
        # 结束追踪
        final_result = {
            "total_items": len(results),
            "results": results,
            "command_id": command_id
        }
        tracer.end_command_trace(final_result)
        
        # 保存报告
        report_file = tracer.save_flow_report()
        
        return final_result, report_file
        
    except Exception as e:
        tracer.logger.error(f"命令执行过程中出现错误: {e}")
        tracer.logger.error(traceback.format_exc())
        
        # 即使出错也要保存已收集的追踪信息
        if tracer.current_command:
            tracer.end_command_trace({"error": str(e), "traceback": traceback.format_exc()})
            
        report_file = tracer.save_flow_report()
        raise

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Magentic-One 命令流程追踪器")
    parser.add_argument("--scenario", required=True, help="场景ID")
    parser.add_argument("--command", help="要追踪的命令 (如果不提供则从场景文件读取)")
    parser.add_argument("--output-dir", help="输出目录", default="debug")
    
    args = parser.parse_args()
    
    # 如果没有提供命令，从场景文件读取
    command = args.command
    if not command:
        scenario_file = Path(f"Agents_Failure_Attribution/Who&When/Hand-Crafted/{args.scenario}.json")
        if scenario_file.exists():
            with scenario_file.open(encoding='utf-8') as f:
                scenario = json.load(f)
            command = scenario["question"]
        else:
            print(f"错误: 场景文件不存在: {scenario_file}")
            sys.exit(1)
    
    print(f"🔍 启动命令流程追踪器...")
    print(f"📋 场景: {args.scenario}")
    print(f"💬 命令: {command}")
    print(f"📁 输出目录: {args.output_dir}")
    
    try:
        result, report_file = asyncio.run(
            trace_command_execution(args.scenario, command, Path(args.output_dir))
        )
        print(f"\n✅ 追踪完成!")
        print(f"📊 处理了 {result['total_items']} 个响应项目")
        print(f"📄 详细报告: {report_file}")
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        sys.exit(1)