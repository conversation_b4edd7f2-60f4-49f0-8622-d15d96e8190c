#!/usr/bin/env python3
"""
Magentic-One 调试套件启动器
=========================

一键运行所有调试工具的便捷脚本。

使用方法:
    python run_debug_suite.py --scenario 1 --mode all
    python run_debug_suite.py --scenario 1 --mode orchestrator
    python run_debug_suite.py --scenario 1 --mode visualization
"""

import os
import sys
import asyncio
import argparse
import subprocess
from pathlib import Path
from datetime import datetime


class DebugSuiteLauncher:
    """调试套件启动器"""
    
    def __init__(self, scenario_id: str):
        self.scenario_id = scenario_id
        self.script_dir = Path(__file__).parent
        self.results = {}
        
    def print_banner(self):
        """打印启动横幅"""
        print("=" * 80)
        print("🔍 Magentic-One 深度调试套件")
        print("=" * 80)
        print(f"📋 场景ID: {self.scenario_id}")
        print(f"🕒 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"📁 脚本目录: {self.script_dir}")
        print("=" * 80)
    
    def check_prerequisites(self):
        """检查运行前提条件"""
        print("\n🔧 检查运行环境...")
        
        # 检查 Ollama 服务
        try:
            import requests
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                print("✅ Ollama 服务正常运行")
            else:
                print("❌ Ollama 服务响应异常")
                return False
        except Exception as e:
            print(f"❌ 无法连接到 Ollama 服务: {e}")
            print("💡 请确保 Ollama 服务正在运行: ollama serve")
            return False
        
        # 检查场景文件
        scenario_file = Path(f"Agents_Failure_Attribution/Who&When/Hand-Crafted/{self.scenario_id}.json")
        if scenario_file.exists():
            print(f"✅ 场景文件存在: {scenario_file}")
        else:
            print(f"❌ 场景文件不存在: {scenario_file}")
            return False
        
        # 检查必要的目录
        for dir_name in ["logs", "logs/debug", "logs/orchestrator_analysis", "logs/visualization"]:
            Path(dir_name).mkdir(parents=True, exist_ok=True)
        print("✅ 日志目录已准备就绪")
        
        return True
    
    async def run_basic_debug(self):
        """运行基础调试"""
        print("\n🚀 启动基础调试...")
        try:
            from debug_magentic_one import run_debug_session
            await run_debug_session(self.scenario_id, "detailed")
            self.results["basic_debug"] = "✅ 完成"
            print("✅ 基础调试完成")
        except Exception as e:
            self.results["basic_debug"] = f"❌ 失败: {str(e)}"
            print(f"❌ 基础调试失败: {e}")
    
    async def run_orchestrator_analysis(self):
        """运行 Orchestrator 分析"""
        print("\n🧠 启动 Orchestrator 分析...")
        try:
            from orchestrator_analyzer import run_orchestrator_analysis
            report_file = await run_orchestrator_analysis(self.scenario_id)
            self.results["orchestrator_analysis"] = f"✅ 完成 - {report_file}"
            print("✅ Orchestrator 分析完成")
        except Exception as e:
            self.results["orchestrator_analysis"] = f"❌ 失败: {str(e)}"
            print(f"❌ Orchestrator 分析失败: {e}")
    
    async def run_message_visualization(self):
        """运行消息流可视化"""
        print("\n🎨 启动消息流可视化...")
        try:
            from message_flow_visualizer import run_message_flow_visualization
            html_file = await run_message_flow_visualization(self.scenario_id)
            self.results["message_visualization"] = f"✅ 完成 - {html_file}"
            print("✅ 消息流可视化完成")
        except Exception as e:
            self.results["message_visualization"] = f"❌ 失败: {str(e)}"
            print(f"❌ 消息流可视化失败: {e}")
    
    async def run_all_tools(self):
        """运行所有调试工具"""
        print("\n🔄 运行完整调试套件...")
        
        # 按顺序运行各个工具
        await self.run_basic_debug()
        await self.run_orchestrator_analysis()
        await self.run_message_visualization()
    
    def print_summary(self):
        """打印执行摘要"""
        print("\n" + "=" * 80)
        print("📊 调试套件执行摘要")
        print("=" * 80)
        
        for tool_name, result in self.results.items():
            print(f"{tool_name:25}: {result}")
        
        print("\n📁 输出文件位置:")
        print("   - 基础调试日志: logs/debug/")
        print("   - Orchestrator 分析: logs/orchestrator_analysis/")
        print("   - 消息流可视化: logs/visualization/")
        
        print("\n💡 下一步建议:")
        print("   1. 查看 HTML 可视化报告了解消息流")
        print("   2. 分析 Orchestrator 决策报告")
        print("   3. 检查详细调试日志了解执行细节")
        print("=" * 80)


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="Magentic-One 调试套件启动器")
    parser.add_argument("--scenario", required=True, help="场景ID (例如: 1)")
    parser.add_argument("--mode", choices=["all", "basic", "orchestrator", "visualization"], 
                       default="all", help="运行模式")
    
    args = parser.parse_args()
    
    # 切换到脚本目录
    os.chdir(Path(__file__).parent)
    
    launcher = DebugSuiteLauncher(args.scenario)
    launcher.print_banner()
    
    # 检查前提条件
    if not launcher.check_prerequisites():
        print("\n❌ 前提条件检查失败，请解决上述问题后重试")
        sys.exit(1)
    
    try:
        # 根据模式运行相应的工具
        if args.mode == "all":
            await launcher.run_all_tools()
        elif args.mode == "basic":
            await launcher.run_basic_debug()
        elif args.mode == "orchestrator":
            await launcher.run_orchestrator_analysis()
        elif args.mode == "visualization":
            await launcher.run_message_visualization()
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断执行")
    except Exception as e:
        print(f"\n❌ 执行过程中出现错误: {e}")
    finally:
        launcher.print_summary()


if __name__ == "__main__":
    asyncio.run(main())
