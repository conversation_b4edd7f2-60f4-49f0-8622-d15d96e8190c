#!/usr/bin/env python3
"""
Magentic-One 深度调试脚本
========================

这个脚本提供了全面的调试功能来追踪 Magentic-One 的详细执行流程，
包括 Orchestrator 的决策过程、智能体间的通信、以及内部状态变化。

使用方法:
    python debug_magentic_one.py --scenario 1 --debug-level detailed
"""

import os
import json
import asyncio
import logging
import traceback
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional
from dataclasses import dataclass, asdict

# AutoGen 相关导入
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor

# 调试相关导入
import inspect
import functools
from contextlib import contextmanager


@dataclass
class DebugEvent:
    """调试事件数据结构"""
    timestamp: str
    event_type: str  # 'orchestrator_decision', 'agent_communication', 'llm_call', 'state_change'
    source: str      # 事件来源 (orchestrator, websurfer, filesurfer, etc.)
    target: Optional[str] = None  # 事件目标
    data: Dict[str, Any] = None
    call_stack: List[str] = None
    
    def __post_init__(self):
        if self.data is None:
            self.data = {}
        if self.call_stack is None:
            self.call_stack = []


class MagenticOneDebugger:
    """Magentic-One 调试器"""
    
    def __init__(self, scenario_id: str, debug_level: str = "basic"):
        self.scenario_id = scenario_id
        self.debug_level = debug_level  # basic, detailed, verbose
        self.events: List[DebugEvent] = []
        self.current_step = 0
        
        # 创建调试日志目录
        self.debug_dir = Path("logs/debug")
        self.debug_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志记录
        self.setup_logging()
        
    def setup_logging(self):
        """设置详细的日志记录"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        log_file = self.debug_dir / f"debug_scenario_{self.scenario_id}_{timestamp}.log"
        
        # 配置根日志记录器
        logging.basicConfig(
            level=logging.DEBUG,
            format='%(asctime)s.%(msecs)03d [%(levelname)s] %(name)s: %(message)s',
            datefmt='%H:%M:%S',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger("MagenticOneDebugger")
        self.logger.info(f"调试会话开始 - 场景 {self.scenario_id}, 调试级别: {self.debug_level}")
        
    def log_event(self, event_type: str, source: str, data: Dict[str, Any], 
                  target: str = None):
        """记录调试事件"""
        # 获取调用栈信息
        call_stack = []
        if self.debug_level in ["detailed", "verbose"]:
            stack = inspect.stack()[1:6]  # 获取前5层调用栈
            call_stack = [f"{frame.filename}:{frame.lineno} in {frame.function}" 
                         for frame in stack]
        
        event = DebugEvent(
            timestamp=datetime.now(timezone.utc).isoformat(),
            event_type=event_type,
            source=source,
            target=target,
            data=data,
            call_stack=call_stack
        )
        
        self.events.append(event)
        
        # 实时日志输出
        self.logger.info(f"[{event_type}] {source} -> {target or 'N/A'}: {data.get('summary', str(data)[:100])}")
        
        if self.debug_level == "verbose":
            self.logger.debug(f"详细数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
            self.logger.debug(f"调用栈: {call_stack}")
    
    def create_instrumented_client(self, original_client):
        """创建带调试功能的模型客户端"""
        class InstrumentedClient:
            def __init__(self, original, debugger):
                self._original = original
                self._debugger = debugger
                # 复制原始客户端的所有属性
                for attr in dir(original):
                    if not attr.startswith('_') and attr != 'create':
                        setattr(self, attr, getattr(original, attr))
            
            async def create(self, messages, **kwargs):
                """拦截并记录 LLM 调用"""
                # 记录输入
                input_data = {
                    "messages": [{"role": msg.role if hasattr(msg, 'role') else str(type(msg)), 
                                 "content": str(msg.content)[:500] + "..." if len(str(msg.content)) > 500 else str(msg.content)}
                                for msg in messages],
                    "kwargs": {k: str(v) for k, v in kwargs.items()}
                }
                
                self._debugger.log_event(
                    "llm_call_start", 
                    "model_client", 
                    {"input": input_data, "summary": f"LLM调用开始，{len(messages)}条消息"}
                )
                
                # 执行原始调用
                start_time = datetime.now()
                try:
                    response = await self._original.create(messages, **kwargs)
                    end_time = datetime.now()
                    
                    # 记录输出
                    output_data = {
                        "response_content": str(response.content)[:1000] + "..." if len(str(response.content)) > 1000 else str(response.content),
                        "duration_ms": (end_time - start_time).total_seconds() * 1000,
                        "response_type": str(type(response))
                    }
                    
                    self._debugger.log_event(
                        "llm_call_complete",
                        "model_client",
                        {"output": output_data, "summary": f"LLM调用完成，耗时{output_data['duration_ms']:.1f}ms"}
                    )
                    
                    return response
                    
                except Exception as e:
                    self._debugger.log_event(
                        "llm_call_error",
                        "model_client", 
                        {"error": str(e), "traceback": traceback.format_exc()}
                    )
                    raise
        
        return InstrumentedClient(original_client, self)
    
    def instrument_orchestrator(self, orchestrator):
        """为 Orchestrator 添加调试功能"""
        # 保存原始方法
        original_orchestrate_step = orchestrator._orchestrate_step
        original_reenter_outer_loop = orchestrator._reenter_outer_loop
        
        async def debug_orchestrate_step(cancellation_token):
            """调试版本的 _orchestrate_step"""
            self.current_step += 1
            self.log_event(
                "orchestrator_step_start",
                "orchestrator",
                {"step": self.current_step, "summary": f"开始执行第{self.current_step}步"}
            )
            
            try:
                result = await original_orchestrate_step(cancellation_token)
                self.log_event(
                    "orchestrator_step_complete", 
                    "orchestrator",
                    {"step": self.current_step, "summary": f"第{self.current_step}步完成"}
                )
                return result
            except Exception as e:
                self.log_event(
                    "orchestrator_step_error",
                    "orchestrator",
                    {"step": self.current_step, "error": str(e), "traceback": traceback.format_exc()}
                )
                raise
        
        async def debug_reenter_outer_loop(cancellation_token):
            """调试版本的 _reenter_outer_loop (重新思考机制)"""
            self.log_event(
                "orchestrator_rethink_start",
                "orchestrator", 
                {"summary": "触发重新思考机制", "current_stalls": getattr(orchestrator, '_n_stalls', 0)}
            )
            
            try:
                result = await original_reenter_outer_loop(cancellation_token)
                self.log_event(
                    "orchestrator_rethink_complete",
                    "orchestrator",
                    {"summary": "重新思考完成", "new_stalls": getattr(orchestrator, '_n_stalls', 0)}
                )
                return result
            except Exception as e:
                self.log_event(
                    "orchestrator_rethink_error",
                    "orchestrator",
                    {"error": str(e), "traceback": traceback.format_exc()}
                )
                raise
        
        # 替换方法
        orchestrator._orchestrate_step = debug_orchestrate_step
        orchestrator._reenter_outer_loop = debug_reenter_outer_loop
        
        return orchestrator
    
    def save_debug_report(self):
        """保存详细的调试报告"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        report_file = self.debug_dir / f"debug_report_scenario_{self.scenario_id}_{timestamp}.json"
        
        report = {
            "scenario_id": self.scenario_id,
            "debug_level": self.debug_level,
            "total_events": len(self.events),
            "total_steps": self.current_step,
            "events": [asdict(event) for event in self.events],
            "summary": self.generate_summary()
        }
        
        with report_file.open('w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"调试报告已保存到: {report_file}")
        return report_file
    
    def generate_summary(self) -> Dict[str, Any]:
        """生成执行摘要"""
        event_counts = {}
        for event in self.events:
            event_counts[event.event_type] = event_counts.get(event.event_type, 0) + 1
        
        llm_calls = [e for e in self.events if e.event_type.startswith('llm_call')]
        orchestrator_events = [e for e in self.events if e.source == 'orchestrator']
        
        return {
            "event_type_counts": event_counts,
            "total_llm_calls": len([e for e in llm_calls if e.event_type == 'llm_call_start']),
            "total_orchestrator_steps": len([e for e in orchestrator_events if e.event_type == 'orchestrator_step_start']),
            "rethink_triggers": len([e for e in orchestrator_events if e.event_type == 'orchestrator_rethink_start']),
            "execution_timeline": [
                {
                    "timestamp": e.timestamp,
                    "event": e.event_type,
                    "source": e.source,
                    "summary": e.data.get('summary', '')
                }
                for e in self.events[:20]  # 前20个事件
            ]
        }


async def run_debug_session(scenario_id: str, debug_level: str = "detailed"):
    """运行调试会话"""
    debugger = MagenticOneDebugger(scenario_id, debug_level)
    
    try:
        # 加载场景
        scenario_file = Path(f"Agents_Failure_Attribution/Who&When/Hand-Crafted/{scenario_id}.json")
        with scenario_file.open(encoding='utf-8') as f:
            scenario = json.load(f)
        
        query = scenario["question"]
        debugger.logger.info(f"开始调试场景 {scenario_id}: {query}")
        
        # 创建带调试功能的客户端
        original_client = OllamaChatCompletionClient(
            model="llama3.1",
            host="http://localhost:11434"
        )
        instrumented_client = debugger.create_instrumented_client(original_client)
        
        # 创建代码执行器
        code_executor = LocalCommandLineCodeExecutor()
        
        # 创建 MagenticOne 实例
        m1 = MagenticOne(client=instrumented_client, code_executor=code_executor)
        
        # 为 Orchestrator 添加调试功能
        # 注意：需要访问内部的 orchestrator 实例
        debugger.logger.info("正在设置 Orchestrator 调试...")
        
        # 执行任务
        debugger.log_event("task_start", "system", {"query": query, "summary": f"开始执行任务: {query[:100]}"})
        
        async for item in m1.run_stream(task=query):
            if hasattr(item, 'content'):
                role = getattr(item, 'role', None) or getattr(item, 'source', '') or 'assistant'
                content = str(item.content)
                
                debugger.log_event(
                    "agent_message",
                    role,
                    {
                        "content": content[:500] + "..." if len(content) > 500 else content,
                        "full_content_length": len(content),
                        "summary": f"{role} 发送消息，长度: {len(content)}"
                    }
                )
        
        debugger.log_event("task_complete", "system", {"summary": "任务执行完成"})
        
    except Exception as e:
        debugger.log_event("task_error", "system", {
            "error": str(e),
            "traceback": traceback.format_exc(),
            "summary": f"任务执行出错: {str(e)}"
        })
        raise
    finally:
        # 保存调试报告
        report_file = debugger.save_debug_report()
        print(f"\n🔍 调试报告已保存到: {report_file}")
        print(f"📊 总共记录了 {len(debugger.events)} 个调试事件")
        print(f"🔄 执行了 {debugger.current_step} 个 Orchestrator 步骤")


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Magentic-One 深度调试工具")
    parser.add_argument("--scenario", required=True, help="场景ID (例如: 1)")
    parser.add_argument("--debug-level", choices=["basic", "detailed", "verbose"], 
                       default="detailed", help="调试级别")
    
    args = parser.parse_args()
    
    print(f"🚀 启动 Magentic-One 深度调试...")
    print(f"📋 场景: {args.scenario}")
    print(f"🔍 调试级别: {args.debug_level}")
    print(f"📁 调试日志将保存到: logs/debug/")
    
    asyncio.run(run_debug_session(args.scenario, args.debug_level))
