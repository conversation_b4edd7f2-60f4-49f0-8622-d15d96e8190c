#!/usr/bin/env python3
"""
Magentic-One 消息流可视化工具
===========================

可视化智能体间的消息传递和通信流程，帮助理解系统的执行流程。

功能:
1. 实时捕获消息流
2. 生成时序图
3. 分析通信模式
4. 导出可视化报告
"""

import json
import asyncio
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional
import html

# AutoGen 相关导入
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor


class MessageFlowVisualizer:
    """消息流可视化器"""
    
    def __init__(self, scenario_id: str):
        self.scenario_id = scenario_id
        self.messages = []
        self.agents = set()
        self.start_time = None
        
        # 创建可视化目录
        self.viz_dir = Path("logs/visualization")
        self.viz_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置日志"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        log_file = self.viz_dir / f"message_flow_{self.scenario_id}_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s.%(msecs)03d [%(levelname)s] %(message)s',
            datefmt='%H:%M:%S',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger("MessageFlowVisualizer")
        self.logger.info(f"🎨 消息流可视化开始 - 场景 {self.scenario_id}")
    
    def record_message(self, source: str, target: str, message_type: str, 
                      content: str, metadata: Dict = None):
        """记录消息"""
        if self.start_time is None:
            self.start_time = datetime.now(timezone.utc)
        
        timestamp = datetime.now(timezone.utc)
        relative_time = (timestamp - self.start_time).total_seconds()
        
        message_record = {
            "id": len(self.messages) + 1,
            "timestamp": timestamp.isoformat(),
            "relative_time_seconds": relative_time,
            "source": source,
            "target": target,
            "message_type": message_type,
            "content_length": len(content),
            "content_preview": content[:100] + "..." if len(content) > 100 else content,
            "full_content": content,
            "metadata": metadata or {}
        }
        
        self.messages.append(message_record)
        self.agents.add(source)
        if target:
            self.agents.add(target)
        
        self.logger.info(f"📨 {source} -> {target or 'ALL'}: {message_type} ({len(content)} chars)")
    
    def create_tracking_client(self, original_client):
        """创建消息追踪版本的客户端"""
        class TrackingClient:
            def __init__(self, original, visualizer):
                self._original = original
                self._visualizer = visualizer
                # 复制所有属性
                for attr in dir(original):
                    if not attr.startswith('_') and attr != 'create':
                        setattr(self, attr, getattr(original, attr))
            
            async def create(self, messages, **kwargs):
                """追踪 LLM 调用"""
                # 记录输入消息
                input_content = "\n".join([str(msg.content) for msg in messages])
                self._visualizer.record_message(
                    "orchestrator", 
                    "llm", 
                    "llm_request",
                    input_content,
                    {"message_count": len(messages)}
                )
                
                # 执行原始调用
                response = await self._original.create(messages, **kwargs)
                
                # 记录输出消息
                self._visualizer.record_message(
                    "llm",
                    "orchestrator", 
                    "llm_response",
                    str(response.content)
                )
                
                return response
        
        return TrackingClient(original_client, self)
    
    def generate_mermaid_diagram(self) -> str:
        """生成 Mermaid 时序图"""
        mermaid_lines = ["sequenceDiagram"]
        
        # 添加参与者
        participants = sorted(list(self.agents))
        for participant in participants:
            mermaid_lines.append(f"    participant {participant}")
        
        # 添加消息
        for msg in self.messages:
            source = msg["source"]
            target = msg["target"] or "ALL"
            message_type = msg["message_type"]
            preview = msg["content_preview"].replace("\n", " ")
            
            # 转义特殊字符
            preview = html.escape(preview)
            
            if target == "ALL":
                # 广播消息
                for participant in participants:
                    if participant != source:
                        mermaid_lines.append(f"    {source}-->{participant}: {message_type}")
            else:
                mermaid_lines.append(f"    {source}->>{target}: {message_type}")
        
        return "\n".join(mermaid_lines)
    
    def generate_html_report(self) -> str:
        """生成 HTML 可视化报告"""
        mermaid_diagram = self.generate_mermaid_diagram()
        
        # 生成消息表格
        message_table_rows = []
        for msg in self.messages:
            row = f"""
            <tr>
                <td>{msg['id']}</td>
                <td>{msg['relative_time_seconds']:.2f}s</td>
                <td><span class="agent-badge {msg['source']}">{msg['source']}</span></td>
                <td><span class="agent-badge {msg['target'] or 'broadcast'}">{msg['target'] or 'ALL'}</span></td>
                <td><span class="message-type">{msg['message_type']}</span></td>
                <td>{msg['content_length']}</td>
                <td class="content-preview">{html.escape(msg['content_preview'])}</td>
            </tr>
            """
            message_table_rows.append(row)
        
        # 生成统计信息
        stats = self.generate_statistics()
        
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Magentic-One 消息流可视化 - 场景 {self.scenario_id}</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; margin: 20px; }}
        .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 10px; margin-bottom: 20px; }}
        .stats {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px; }}
        .stat-card {{ background: #f8f9fa; padding: 15px; border-radius: 8px; border-left: 4px solid #007bff; }}
        .stat-value {{ font-size: 24px; font-weight: bold; color: #007bff; }}
        .diagram-container {{ background: white; padding: 20px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); margin-bottom: 20px; }}
        .message-table {{ width: 100%; border-collapse: collapse; margin-top: 20px; }}
        .message-table th, .message-table td {{ padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }}
        .message-table th {{ background-color: #f8f9fa; font-weight: bold; }}
        .agent-badge {{ padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: bold; }}
        .orchestrator {{ background-color: #e3f2fd; color: #1976d2; }}
        .llm {{ background-color: #f3e5f5; color: #7b1fa2; }}
        .websurfer {{ background-color: #e8f5e8; color: #388e3c; }}
        .filesurfer {{ background-color: #fff3e0; color: #f57c00; }}
        .coder {{ background-color: #fce4ec; color: #c2185b; }}
        .computerterminal {{ background-color: #e0f2f1; color: #00695c; }}
        .broadcast {{ background-color: #f5f5f5; color: #616161; }}
        .message-type {{ background-color: #e1f5fe; color: #0277bd; padding: 2px 6px; border-radius: 3px; font-size: 11px; }}
        .content-preview {{ max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🎨 Magentic-One 消息流可视化</h1>
        <p>场景 {self.scenario_id} - 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    </div>
    
    <div class="stats">
        <div class="stat-card">
            <div class="stat-value">{stats['total_messages']}</div>
            <div>总消息数</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{len(self.agents)}</div>
            <div>参与智能体</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{stats['total_duration']:.1f}s</div>
            <div>总执行时间</div>
        </div>
        <div class="stat-card">
            <div class="stat-value">{stats['avg_message_interval']:.2f}s</div>
            <div>平均消息间隔</div>
        </div>
    </div>
    
    <div class="diagram-container">
        <h2>📊 消息时序图</h2>
        <div class="mermaid">
{mermaid_diagram}
        </div>
    </div>
    
    <div class="diagram-container">
        <h2>📋 详细消息列表</h2>
        <table class="message-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>时间</th>
                    <th>发送者</th>
                    <th>接收者</th>
                    <th>类型</th>
                    <th>长度</th>
                    <th>内容预览</th>
                </tr>
            </thead>
            <tbody>
                {''.join(message_table_rows)}
            </tbody>
        </table>
    </div>
    
    <script>
        mermaid.initialize({{ startOnLoad: true, theme: 'default' }});
    </script>
</body>
</html>
        """
        
        return html_content
    
    def generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        if not self.messages:
            return {}
        
        total_duration = self.messages[-1]["relative_time_seconds"] if self.messages else 0
        
        # 按消息类型统计
        message_type_counts = {}
        for msg in self.messages:
            msg_type = msg["message_type"]
            message_type_counts[msg_type] = message_type_counts.get(msg_type, 0) + 1
        
        # 按智能体统计
        agent_message_counts = {}
        for msg in self.messages:
            source = msg["source"]
            agent_message_counts[source] = agent_message_counts.get(source, 0) + 1
        
        return {
            "total_messages": len(self.messages),
            "total_duration": total_duration,
            "avg_message_interval": total_duration / len(self.messages) if self.messages else 0,
            "message_type_distribution": message_type_counts,
            "agent_message_distribution": agent_message_counts,
            "unique_agents": len(self.agents)
        }
    
    def save_visualization(self):
        """保存可视化结果"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        
        # 保存 HTML 报告
        html_content = self.generate_html_report()
        html_file = self.viz_dir / f"message_flow_report_{self.scenario_id}_{timestamp}.html"
        with html_file.open('w', encoding='utf-8') as f:
            f.write(html_content)
        
        # 保存原始数据
        data_file = self.viz_dir / f"message_flow_data_{self.scenario_id}_{timestamp}.json"
        with data_file.open('w', encoding='utf-8') as f:
            json.dump({
                "scenario_id": self.scenario_id,
                "messages": self.messages,
                "statistics": self.generate_statistics()
            }, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"🎨 可视化报告已保存到: {html_file}")
        return html_file, data_file


async def run_message_flow_visualization(scenario_id: str):
    """运行消息流可视化"""
    visualizer = MessageFlowVisualizer(scenario_id)
    
    try:
        # 加载场景
        scenario_file = Path(f"Agents_Failure_Attribution/Who&When/Hand-Crafted/{scenario_id}.json")
        with scenario_file.open(encoding='utf-8') as f:
            scenario = json.load(f)
        
        query = scenario["question"]
        visualizer.logger.info(f"🎯 开始可视化场景 {scenario_id}: {query}")
        
        # 创建追踪版本的客户端
        original_client = OllamaChatCompletionClient(
            model="llama3.1",
            host="http://localhost:11434"
        )
        tracking_client = visualizer.create_tracking_client(original_client)
        
        # 创建 MagenticOne 实例
        code_executor = LocalCommandLineCodeExecutor()
        m1 = MagenticOne(client=tracking_client, code_executor=code_executor)
        
        # 记录初始任务
        visualizer.record_message("user", "orchestrator", "task_input", query)
        
        # 执行任务并追踪消息
        async for item in m1.run_stream(task=query):
            if hasattr(item, 'content'):
                role = getattr(item, 'role', None) or getattr(item, 'source', '') or 'assistant'
                content = str(item.content)
                
                # 记录智能体消息
                visualizer.record_message(
                    role, 
                    "orchestrator",  # 大多数消息都是发给 orchestrator 的
                    "agent_response",
                    content
                )
        
        visualizer.logger.info("✅ 任务执行完成，正在生成可视化报告...")
        
    except Exception as e:
        visualizer.logger.error(f"❌ 可视化过程中出错: {str(e)}")
        raise
    finally:
        # 保存可视化结果
        html_file, data_file = visualizer.save_visualization()
        stats = visualizer.generate_statistics()
        
        # 打印摘要
        print(f"\n🎨 消息流可视化完成!")
        print(f"📨 总消息数: {stats['total_messages']}")
        print(f"🤖 参与智能体: {stats['unique_agents']}")
        print(f"⏱️ 总执行时间: {stats['total_duration']:.1f}秒")
        print(f"📊 HTML报告: {html_file}")
        print(f"📁 原始数据: {data_file}")
        
        return html_file


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Magentic-One 消息流可视化工具")
    parser.add_argument("--scenario", required=True, help="场景ID (例如: 1)")
    
    args = parser.parse_args()
    
    print(f"🎨 启动消息流可视化...")
    print(f"📋 场景: {args.scenario}")
    
    asyncio.run(run_message_flow_visualization(args.scenario))
