#!/usr/bin/env python3
"""
Magentic-One Orchestrator 深度分析器
==================================

专门用于分析 Orchestrator 的内部决策过程、状态变化和消息处理流程。

功能:
1. 拦截并记录所有 LLM 调用的输入输出
2. 追踪 Orchestrator 的内部状态变化
3. 分析决策逻辑和重新思考机制
4. 可视化智能体通信流程
"""

import json
import asyncio
import logging
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional
import inspect
import re

# AutoGen 相关导入
from autogen_ext.models.ollama import OllamaChatCompletionClient
from autogen_ext.teams.magentic_one import MagenticOne
from autogen_ext.code_executors.local import LocalCommandLineCodeExecutor


class OrchestratorAnalyzer:
    """Orchestrator 分析器"""
    
    def __init__(self, scenario_id: str):
        self.scenario_id = scenario_id
        self.analysis_data = {
            "llm_calls": [],
            "orchestrator_states": [],
            "decision_points": [],
            "agent_communications": [],
            "rethink_events": []
        }
        
        # 创建分析日志目录
        self.analysis_dir = Path("logs/orchestrator_analysis")
        self.analysis_dir.mkdir(parents=True, exist_ok=True)
        
        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置分析日志"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        log_file = self.analysis_dir / f"orchestrator_analysis_{self.scenario_id}_{timestamp}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s.%(msecs)03d [%(levelname)s] %(message)s',
            datefmt='%H:%M:%S',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger("OrchestratorAnalyzer")
        self.logger.info(f"🔍 Orchestrator 分析开始 - 场景 {self.scenario_id}")
    
    def create_analyzing_client(self, original_client):
        """创建分析版本的模型客户端"""
        class AnalyzingClient:
            def __init__(self, original, analyzer):
                self._original = original
                self._analyzer = analyzer
                # 复制所有属性
                for attr in dir(original):
                    if not attr.startswith('_') and attr != 'create':
                        setattr(self, attr, getattr(original, attr))
            
            async def create(self, messages, **kwargs):
                """拦截并分析 LLM 调用"""
                call_id = len(self._analyzer.analysis_data["llm_calls"]) + 1
                timestamp = datetime.now(timezone.utc).isoformat()
                
                # 分析输入消息
                input_analysis = self._analyzer.analyze_llm_input(messages)
                
                self._analyzer.logger.info(f"📤 LLM调用 #{call_id}: {input_analysis['call_type']}")
                
                # 记录调用开始
                call_record = {
                    "call_id": call_id,
                    "timestamp": timestamp,
                    "input_analysis": input_analysis,
                    "messages": [
                        {
                            "role": getattr(msg, 'role', 'unknown'),
                            "content": str(msg.content),
                            "source": getattr(msg, 'source', 'unknown')
                        }
                        for msg in messages
                    ]
                }
                
                # 执行原始调用
                start_time = datetime.now()
                try:
                    response = await self._original.create(messages, **kwargs)
                    end_time = datetime.now()
                    
                    # 分析输出
                    output_analysis = self._analyzer.analyze_llm_output(
                        str(response.content), input_analysis['call_type']
                    )
                    
                    call_record.update({
                        "response_content": str(response.content),
                        "output_analysis": output_analysis,
                        "duration_ms": (end_time - start_time).total_seconds() * 1000,
                        "success": True
                    })
                    
                    self._analyzer.logger.info(f"📥 LLM响应 #{call_id}: {output_analysis['summary']}")
                    
                    # 如果是决策调用，记录决策点
                    if input_analysis['call_type'] == 'progress_ledger':
                        self._analyzer.record_decision_point(call_record, output_analysis)
                    
                    return response
                    
                except Exception as e:
                    call_record.update({
                        "error": str(e),
                        "success": False,
                        "duration_ms": (datetime.now() - start_time).total_seconds() * 1000
                    })
                    self._analyzer.logger.error(f"❌ LLM调用 #{call_id} 失败: {str(e)}")
                    raise
                finally:
                    self._analyzer.analysis_data["llm_calls"].append(call_record)
        
        return AnalyzingClient(original_client, self)
    
    def analyze_llm_input(self, messages) -> Dict[str, Any]:
        """分析 LLM 输入，确定调用类型和目的"""
        if not messages:
            return {"call_type": "unknown", "purpose": "empty_input"}
        
        # 获取最后一条用户消息的内容
        last_user_msg = None
        for msg in reversed(messages):
            if getattr(msg, 'role', '') == 'user':
                last_user_msg = str(msg.content)
                break
        
        if not last_user_msg:
            return {"call_type": "unknown", "purpose": "no_user_message"}
        
        # 分析消息内容确定调用类型
        content_lower = last_user_msg.lower()
        
        if "pre-survey" in content_lower and "facts" in content_lower:
            return {
                "call_type": "task_ledger_facts",
                "purpose": "收集任务相关事实",
                "stage": "初始化"
            }
        elif "devise a short bullet-point plan" in content_lower:
            return {
                "call_type": "task_ledger_plan", 
                "purpose": "制定执行计划",
                "stage": "初始化"
            }
        elif "is the request fully satisfied" in content_lower:
            return {
                "call_type": "progress_ledger",
                "purpose": "评估进度并决定下一步",
                "stage": "执行中"
            }
        elif "rewrite the following fact sheet" in content_lower:
            return {
                "call_type": "facts_update",
                "purpose": "更新事实清单",
                "stage": "重新思考"
            }
        elif "come up with a new plan" in content_lower:
            return {
                "call_type": "plan_update",
                "purpose": "更新执行计划", 
                "stage": "重新思考"
            }
        elif "provide the final answer" in content_lower:
            return {
                "call_type": "final_answer",
                "purpose": "生成最终答案",
                "stage": "完成"
            }
        else:
            return {
                "call_type": "unknown",
                "purpose": "未识别的调用类型",
                "stage": "unknown"
            }
    
    def analyze_llm_output(self, response_content: str, call_type: str) -> Dict[str, Any]:
        """分析 LLM 输出内容"""
        analysis = {"summary": "", "extracted_data": {}}
        
        if call_type == "progress_ledger":
            # 尝试解析 JSON 决策
            try:
                # 查找 JSON 内容
                json_match = re.search(r'\{.*\}', response_content, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    decision_data = json.loads(json_str)
                    
                    analysis["extracted_data"] = decision_data
                    analysis["summary"] = f"决策: 下一个发言者={decision_data.get('next_speaker', {}).get('answer', 'unknown')}"
                    
                    # 检查是否触发重新思考
                    if not decision_data.get('is_progress_being_made', {}).get('answer', True):
                        analysis["rethink_triggered"] = True
                        analysis["summary"] += " [触发重新思考]"
                else:
                    analysis["summary"] = "无法解析决策JSON"
            except json.JSONDecodeError:
                analysis["summary"] = "JSON解析失败"
        
        elif call_type == "task_ledger_facts":
            # 分析事实收集
            sections = ["GIVEN OR VERIFIED FACTS", "FACTS TO LOOK UP", "FACTS TO DERIVE", "EDUCATED GUESSES"]
            for section in sections:
                if section in response_content:
                    analysis["extracted_data"][section.lower().replace(" ", "_")] = True
            analysis["summary"] = f"收集了 {len(analysis['extracted_data'])} 类事实"
        
        elif call_type == "task_ledger_plan":
            # 分析计划制定
            bullet_points = len(re.findall(r'^\s*[-*•]\s+', response_content, re.MULTILINE))
            analysis["extracted_data"]["bullet_points"] = bullet_points
            analysis["summary"] = f"制定了包含 {bullet_points} 个要点的计划"
        
        else:
            analysis["summary"] = f"{call_type} 调用完成，响应长度: {len(response_content)}"
        
        return analysis
    
    def record_decision_point(self, call_record: Dict, output_analysis: Dict):
        """记录决策点"""
        if "extracted_data" in output_analysis:
            decision_data = output_analysis["extracted_data"]
            
            decision_point = {
                "timestamp": call_record["timestamp"],
                "call_id": call_record["call_id"],
                "is_request_satisfied": decision_data.get("is_request_satisfied", {}),
                "is_in_loop": decision_data.get("is_in_loop", {}),
                "is_progress_being_made": decision_data.get("is_progress_being_made", {}),
                "next_speaker": decision_data.get("next_speaker", {}),
                "instruction": decision_data.get("instruction_or_question", {}),
                "rethink_triggered": output_analysis.get("rethink_triggered", False)
            }
            
            self.analysis_data["decision_points"].append(decision_point)
            
            # 记录重新思考事件
            if decision_point["rethink_triggered"]:
                self.analysis_data["rethink_events"].append({
                    "timestamp": call_record["timestamp"],
                    "reason": decision_data.get("is_progress_being_made", {}).get("reason", ""),
                    "loop_detected": decision_data.get("is_in_loop", {}).get("answer", False)
                })
    
    def save_analysis_report(self):
        """保存分析报告"""
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d_%H%M%S")
        report_file = self.analysis_dir / f"orchestrator_report_{self.scenario_id}_{timestamp}.json"
        
        # 生成统计信息
        stats = self.generate_statistics()
        
        report = {
            "scenario_id": self.scenario_id,
            "analysis_timestamp": timestamp,
            "statistics": stats,
            "detailed_data": self.analysis_data
        }
        
        with report_file.open('w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        self.logger.info(f"📊 分析报告已保存到: {report_file}")
        return report_file, stats
    
    def generate_statistics(self) -> Dict[str, Any]:
        """生成统计信息"""
        llm_calls = self.analysis_data["llm_calls"]
        decision_points = self.analysis_data["decision_points"]
        rethink_events = self.analysis_data["rethink_events"]
        
        # 按调用类型统计
        call_type_counts = {}
        for call in llm_calls:
            call_type = call.get("input_analysis", {}).get("call_type", "unknown")
            call_type_counts[call_type] = call_type_counts.get(call_type, 0) + 1
        
        # 决策统计
        next_speakers = [dp.get("next_speaker", {}).get("answer", "") for dp in decision_points]
        speaker_counts = {}
        for speaker in next_speakers:
            if speaker:
                speaker_counts[speaker] = speaker_counts.get(speaker, 0) + 1
        
        return {
            "total_llm_calls": len(llm_calls),
            "call_type_distribution": call_type_counts,
            "total_decision_points": len(decision_points),
            "rethink_events_count": len(rethink_events),
            "speaker_selection_distribution": speaker_counts,
            "average_call_duration_ms": sum(call.get("duration_ms", 0) for call in llm_calls) / len(llm_calls) if llm_calls else 0,
            "execution_stages": {
                "initialization": len([c for c in llm_calls if c.get("input_analysis", {}).get("stage") == "初始化"]),
                "execution": len([c for c in llm_calls if c.get("input_analysis", {}).get("stage") == "执行中"]),
                "rethinking": len([c for c in llm_calls if c.get("input_analysis", {}).get("stage") == "重新思考"]),
                "completion": len([c for c in llm_calls if c.get("input_analysis", {}).get("stage") == "完成"])
            }
        }


async def run_orchestrator_analysis(scenario_id: str):
    """运行 Orchestrator 分析"""
    analyzer = OrchestratorAnalyzer(scenario_id)
    
    try:
        # 加载场景
        scenario_file = Path(f"Agents_Failure_Attribution/Who&When/Hand-Crafted/{scenario_id}.json")
        with scenario_file.open(encoding='utf-8') as f:
            scenario = json.load(f)
        
        query = scenario["question"]
        analyzer.logger.info(f"🎯 开始分析场景 {scenario_id}: {query}")
        
        # 创建分析版本的客户端
        original_client = OllamaChatCompletionClient(
            model="llama3.1",
            host="http://localhost:11434"
        )
        analyzing_client = analyzer.create_analyzing_client(original_client)
        
        # 创建 MagenticOne 实例
        code_executor = LocalCommandLineCodeExecutor()
        m1 = MagenticOne(client=analyzing_client, code_executor=code_executor)
        
        # 执行任务
        async for item in m1.run_stream(task=query):
            if hasattr(item, 'content'):
                role = getattr(item, 'role', None) or getattr(item, 'source', '') or 'assistant'
                content = str(item.content)
                
                # 记录智能体通信
                analyzer.analysis_data["agent_communications"].append({
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                    "role": role,
                    "content_length": len(content),
                    "content_preview": content[:200] + "..." if len(content) > 200 else content
                })
        
        analyzer.logger.info("✅ 任务执行完成，正在生成分析报告...")
        
    except Exception as e:
        analyzer.logger.error(f"❌ 分析过程中出错: {str(e)}")
        raise
    finally:
        # 保存分析报告
        report_file, stats = analyzer.save_analysis_report()
        
        # 打印摘要
        print(f"\n🎯 Orchestrator 分析完成!")
        print(f"📊 总计 LLM 调用: {stats['total_llm_calls']}")
        print(f"🔄 决策点: {stats['total_decision_points']}")
        print(f"🤔 重新思考事件: {stats['rethink_events_count']}")
        print(f"📁 详细报告: {report_file}")
        
        return report_file


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Magentic-One Orchestrator 深度分析器")
    parser.add_argument("--scenario", required=True, help="场景ID (例如: 1)")
    
    args = parser.parse_args()
    
    print(f"🔍 启动 Orchestrator 深度分析...")
    print(f"📋 场景: {args.scenario}")
    
    asyncio.run(run_orchestrator_analysis(args.scenario))
