# Magentic-One 调试工具集

这个文件夹包含了专门用于调试和分析 Magentic-One 系统的工具集。

## 🔧 工具列表

### 1. `debug_magentic_one.py` - 全面调试脚本
**功能**: 提供多级别的全面调试功能
- 拦截并记录所有 LLM 调用
- 追踪 Orchestrator 的执行步骤
- 记录智能体间的通信
- 监控系统内部状态变化

**使用方法**:
```bash
python scripts/debug/debug_magentic_one.py --scenario 1 --debug-level detailed
```

**调试级别**:
- `basic`: 基础事件记录
- `detailed`: 详细的 LLM 调用和状态信息
- `verbose`: 包含完整调用栈的详尽信息

### 2. `orchestrator_analyzer.py` - Orchestrator 深度分析器
**功能**: 专门分析 Orchestrator 的决策过程
- 分析每次 LLM 调用的类型和目的
- 解析决策 JSON 的内容
- 追踪重新思考机制的触发
- 统计智能体选择模式

**使用方法**:
```bash
python scripts/debug/orchestrator_analyzer.py --scenario 1
```

**分析内容**:
- 任务事实收集 (`task_ledger_facts`)
- 执行计划制定 (`task_ledger_plan`)
- 进度评估决策 (`progress_ledger`)
- 重新思考更新 (`facts_update`, `plan_update`)

### 3. `message_flow_visualizer.py` - 消息流可视化工具
**功能**: 可视化智能体间的消息传递
- 生成交互式 HTML 报告
- 创建时序图展示通信流程
- 统计消息类型和频率
- 分析通信模式

**使用方法**:
```bash
python scripts/debug/message_flow_visualizer.py --scenario 1
```

**输出**:
- HTML 可视化报告（包含 Mermaid 时序图）
- 详细的消息列表和统计信息

### 4. `run_debug_suite.py` - 一键启动器
**功能**: 批量运行所有调试工具
- 检查运行环境和前提条件
- 按顺序执行各个调试工具
- 生成综合执行摘要

**使用方法**:
```bash
# 运行所有工具
python scripts/debug/run_debug_suite.py --scenario 1 --mode all

# 只运行特定工具
python scripts/debug/run_debug_suite.py --scenario 1 --mode orchestrator
```

## 📁 输出文件结构

```
logs/
├── debug/                          # 基础调试输出
│   ├── debug_scenario_1_*.log      # 调试日志
│   └── debug_report_scenario_1_*.json  # 调试报告
├── orchestrator_analysis/          # Orchestrator 分析输出
│   ├── orchestrator_analysis_1_*.log   # 分析日志
│   └── orchestrator_report_1_*.json    # 分析报告
└── visualization/                  # 可视化输出
    ├── message_flow_1_*.log        # 可视化日志
    ├── message_flow_report_1_*.html # HTML 报告
    └── message_flow_data_1_*.json  # 原始数据
```

## 🎯 使用场景

### 场景 1: 理解整体执行流程
```bash
python scripts/debug/debug_magentic_one.py --scenario 1 --debug-level basic
```
适合初次了解系统的执行流程和主要事件。

### 场景 2: 分析决策逻辑
```bash
python scripts/debug/orchestrator_analyzer.py --scenario 1
```
深入理解 Orchestrator 如何做决策，何时触发重新思考。

### 场景 3: 可视化通信流程
```bash
python scripts/debug/message_flow_visualizer.py --scenario 1
```
直观了解智能体间的消息传递和通信模式。

### 场景 4: 全面调试分析
```bash
python scripts/debug/run_debug_suite.py --scenario 1 --mode all
```
一次性运行所有工具，获得完整的调试信息。

## 🔍 关键调试信息

### LLM 调用类型识别
- `task_ledger_facts`: 收集任务相关事实
- `task_ledger_plan`: 制定执行计划
- `progress_ledger`: 评估进度并决定下一步
- `facts_update`: 更新事实清单（重新思考时）
- `plan_update`: 更新执行计划（重新思考时）
- `final_answer`: 生成最终答案

### 重新思考机制
当 `is_progress_being_made = false` 时触发，包括：
1. 更新事实清单
2. 重新制定计划
3. 重置失败计数器

### 智能体通信协议
- 基于发布-订阅模式
- 使用 `GroupChatMessage` 包装
- 通过 AutoGen Core Runtime 传递

## 💡 调试技巧

1. **从简单到复杂**: 先用 basic 级别了解整体，再用 detailed 深入分析
2. **结合多个工具**: 用可视化理解流程，用分析器理解决策
3. **关注关键指标**: LLM 调用次数、重新思考频率、智能体选择分布
4. **对比不同场景**: 比较成功和失败场景的执行模式

## ⚠️ 注意事项

1. 确保 Ollama 服务正在运行 (`ollama serve`)
2. 确保场景文件存在于正确路径
3. 调试工具会增加执行时间，用于分析而非生产环境
4. 大量调试信息可能占用较多磁盘空间

## 🆘 故障排除

### Ollama 连接问题
```bash
# 检查服务状态
curl http://localhost:11434/api/tags

# 启动服务
ollama serve
```

### 权限问题
```bash
chmod +x scripts/debug/*.py
```

### 依赖问题
```bash
pip install -r requirements.txt
```
