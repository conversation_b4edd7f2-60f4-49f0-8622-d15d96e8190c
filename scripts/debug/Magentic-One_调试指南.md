# Magentic-One 深度调试指南

## 概述

本指南提供了全面的 Magentic-One 调试方案，帮助您深入理解系统的执行流程、智能体通信和内部决策机制。

## 🔧 调试工具概览

### 1. 基础调试工具
- **`debug_magentic_one.py`**: 全面的调试脚本，提供多级别调试功能
- **`orchestrator_analyzer.py`**: 专门分析 Orchestrator 的决策过程
- **`message_flow_visualizer.py`**: 可视化智能体间的消息流

### 2. 现有工具
- **`run_m1_test.py`**: 基础的测试运行脚本，已包含日志功能

## 🚀 快速开始

### 基础调试运行
```bash
# 运行基础调试
python scripts/debug_magentic_one.py --scenario 1 --debug-level basic

# 运行详细调试
python scripts/debug_magentic_one.py --scenario 1 --debug-level detailed

# 运行详尽调试（包含完整调用栈）
python scripts/debug_magentic_one.py --scenario 1 --debug-level verbose
```

### Orchestrator 深度分析
```bash
# 分析 Orchestrator 的决策过程
python scripts/orchestrator_analyzer.py --scenario 1
```

### 消息流可视化
```bash
# 生成消息流可视化报告
python scripts/message_flow_visualizer.py --scenario 1
```

## 📊 调试级别说明

### Basic (基础)
- 记录主要事件和状态变化
- 适合快速了解执行流程

### Detailed (详细)
- 包含 LLM 调用的输入输出
- 记录 Orchestrator 的决策过程
- 追踪智能体间的通信

### Verbose (详尽)
- 包含完整的调用栈信息
- 详细的内部状态变化
- 所有调试事件的完整数据

## 🔍 关键调试点分析

### 1. Orchestrator 接收消息的时机

**关键文件**: `src/autogen-agentchat/src/autogen_agentchat/teams/_group_chat/_magentic_one/_magentic_one_orchestrator.py`

**关键方法**:
- `_orchestrate_step()`: 内循环，处理每个决策步骤
- `_reenter_outer_loop()`: 外循环，触发重新思考机制

**调试要点**:
```python
# 在 _orchestrate_step 中，Orchestrator 接收消息的流程：
1. 检查是否达到最大轮数
2. 更新进度账本 (progress ledger)
3. 调用 LLM 进行决策
4. 解析决策结果
5. 选择下一个发言者
6. 发送指令给选定的智能体
```

### 2. 消息处理的内部流程

**输入格式**:
- 消息通过 `GroupChatMessage` 包装
- 包含 `content`, `role`, `source` 等字段
- 通过发布-订阅机制传递

**处理步骤**:
1. **接收**: 通过 `on_messages` 方法接收消息
2. **解析**: 提取消息内容和元数据
3. **上下文构建**: 将消息添加到对话历史
4. **决策**: 调用 LLM 进行下一步决策
5. **分发**: 将指令发送给选定的智能体

### 3. LLM 调用的详细格式

**Prompt 模板位置**: `src/autogen-agentchat/src/autogen_agentchat/teams/_group_chat/_magentic_one/_prompts.py`

**主要 Prompt 类型**:
- `ORCHESTRATOR_TASK_LEDGER_FACTS_PROMPT`: 收集任务事实
- `ORCHESTRATOR_TASK_LEDGER_PLAN_PROMPT`: 制定执行计划
- `ORCHESTRATOR_PROGRESS_LEDGER_PROMPT`: 评估进度和决策

**输入格式示例**:
```json
{
  "messages": [
    {
      "role": "user",
      "content": "Recall we are working on the following request: ...",
      "source": "orchestrator"
    }
  ]
}
```

**输出格式示例**:
```json
{
  "is_request_satisfied": {
    "reason": "任务尚未完成，需要更多信息",
    "answer": false
  },
  "next_speaker": {
    "reason": "需要搜索网络信息",
    "answer": "WebSurfer"
  },
  "instruction_or_question": {
    "reason": "获取最新数据",
    "answer": "请搜索..."
  }
}
```

### 4. 重新思考机制 (Rethink)

**触发条件**:
- `is_progress_being_made` 为 `false`
- 检测到循环 (`is_in_loop` 为 `true`)
- 连续失败次数达到阈值

**触发位置**:
- `_orchestrate_step()` 方法中检查进度
- 调用 `_reenter_outer_loop()` 重新规划

**重新思考流程**:
1. 更新事实清单 (facts update)
2. 重新制定计划 (plan update)
3. 重置失败计数器
4. 重新开始内循环

## 📁 调试输出文件说明

### 基础日志文件
- `scenario_X_ollama_llama3.1_TIMESTAMP.log`: 基础执行日志
- `scenario_X_ollama_llama3.1_TIMESTAMP.json`: 完整执行记录

### 调试专用文件
- `logs/debug/debug_scenario_X_TIMESTAMP.log`: 详细调试日志
- `logs/debug/debug_report_scenario_X_TIMESTAMP.json`: 调试事件记录

### Orchestrator 分析文件
- `logs/orchestrator_analysis/orchestrator_analysis_X_TIMESTAMP.log`: 分析日志
- `logs/orchestrator_analysis/orchestrator_report_X_TIMESTAMP.json`: 详细分析报告

### 可视化文件
- `logs/visualization/message_flow_report_X_TIMESTAMP.html`: 交互式可视化报告
- `logs/visualization/message_flow_data_X_TIMESTAMP.json`: 原始消息数据

## 🔧 高级调试技巧

### 1. 自定义断点调试
```python
# 在关键位置添加断点
import pdb; pdb.set_trace()

# 或使用条件断点
if some_condition:
    import pdb; pdb.set_trace()
```

### 2. 实时监控状态
```python
# 监控 Orchestrator 内部状态
print(f"当前轮数: {orchestrator._n_rounds}")
print(f"失败次数: {orchestrator._n_stalls}")
print(f"当前任务: {orchestrator._task}")
```

### 3. 自定义日志记录
```python
import logging
logger = logging.getLogger("CustomDebug")
logger.info("自定义调试信息")
```

## 🎯 常见问题调试

### 1. Orchestrator 卡在循环中
**症状**: 重复相同的决策
**调试方法**: 
- 检查 `is_in_loop` 的判断逻辑
- 分析最近几轮的决策历史
- 查看重新思考机制是否正常触发

### 2. 智能体无响应
**症状**: 某个智能体长时间无输出
**调试方法**:
- 检查消息是否正确发送到目标智能体
- 验证智能体的输入处理逻辑
- 查看是否有异常或错误

### 3. LLM 调用失败
**症状**: 模型调用返回错误
**调试方法**:
- 检查 Ollama 服务是否正常运行
- 验证模型名称和参数
- 查看网络连接状态

## 📚 重要源码文件

### 核心文件
1. **Orchestrator 实现**: `src/autogen-agentchat/src/autogen_agentchat/teams/_group_chat/_magentic_one/_magentic_one_orchestrator.py`
2. **Prompt 模板**: `src/autogen-agentchat/src/autogen_agentchat/teams/_group_chat/_magentic_one/_prompts.py`
3. **MagenticOne 主类**: `src/autogen-ext/src/autogen_ext/teams/magentic_one.py`

### 智能体实现
1. **WebSurfer**: `src/autogen-ext/src/autogen_ext/agents/web_surfer/`
2. **FileSurfer**: `src/autogen-ext/src/autogen_ext/agents/file_surfer/`
3. **Coder**: `src/autogen-ext/src/autogen_ext/agents/magentic_one/`

## 🚀 下一步建议

1. **运行基础调试**: 先使用 `debug_magentic_one.py` 了解整体流程
2. **深入分析决策**: 使用 `orchestrator_analyzer.py` 分析决策逻辑
3. **可视化通信**: 使用 `message_flow_visualizer.py` 理解消息流
4. **自定义调试**: 根据具体需求修改调试脚本

通过这些工具和方法，您可以全面理解 Magentic-One 的内部工作机制，包括 Orchestrator 的决策过程、智能体间的通信协议，以及系统的整体执行流程。
