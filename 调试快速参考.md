# Magentic-One 调试快速参考卡片

## 🚀 快速启动命令

```bash
# 一键运行所有调试工具
python scripts/run_debug_suite.py --scenario 1 --mode all

# 只运行 Orchestrator 分析
python scripts/run_debug_suite.py --scenario 1 --mode orchestrator

# 只运行消息流可视化
python scripts/run_debug_suite.py --scenario 1 --mode visualization

# 基础调试（详细级别）
python scripts/debug_magentic_one.py --scenario 1 --debug-level detailed
```

## 📁 关键源码文件位置

### 核心组件
- **Orchestrator**: `src/autogen-agentchat/src/autogen_agentchat/teams/_group_chat/_magentic_one/_magentic_one_orchestrator.py`
- **Prompt 模板**: `src/autogen-agentchat/src/autogen_agentchat/teams/_group_chat/_magentic_one/_prompts.py`
- **MagenticOne 主类**: `src/autogen-ext/src/autogen_ext/teams/magentic_one.py`

### 智能体实现
- **WebSurfer**: `src/autogen-ext/src/autogen_ext/agents/web_surfer/`
- **FileSurfer**: `src/autogen-ext/src/autogen_ext/agents/file_surfer/`
- **Coder**: `src/autogen-ext/src/autogen_ext/agents/magentic_one/`

## 🔍 关键调试点

### 1. Orchestrator 消息接收
**位置**: `_orchestrate_step()` 方法
**关键变量**:
- `self._message_thread`: 消息历史
- `self._n_rounds`: 当前轮数
- `self._n_stalls`: 失败次数

### 2. LLM 调用时机
**输入构建**: `_get_compatible_context()` 方法
**调用位置**: `self._model_client.create()`
**输出解析**: JSON 格式的决策数据

### 3. 重新思考机制
**触发条件**: `is_progress_being_made = false`
**执行位置**: `_reenter_outer_loop()` 方法
**更新内容**: 事实清单 + 执行计划

### 4. 智能体通信
**发送**: `publish_message()` 方法
**接收**: `on_messages()` 方法
**协议**: 发布-订阅模式

## 📊 调试输出文件

### 基础日志
- `logs/generated/scenario_X_ollama_llama3.1_TIMESTAMP.log`
- `logs/generated/scenario_X_ollama_llama3.1_TIMESTAMP.json`

### 调试专用
- `logs/debug/debug_report_scenario_X_TIMESTAMP.json`
- `logs/orchestrator_analysis/orchestrator_report_X_TIMESTAMP.json`
- `logs/visualization/message_flow_report_X_TIMESTAMP.html`

## 🔧 常用调试代码片段

### 添加断点
```python
import pdb; pdb.set_trace()
```

### 监控状态
```python
print(f"轮数: {orchestrator._n_rounds}, 失败: {orchestrator._n_stalls}")
```

### 自定义日志
```python
import logging
logger = logging.getLogger("Debug")
logger.info("调试信息")
```

## 🎯 问题排查清单

### Orchestrator 卡循环
- [ ] 检查 `is_in_loop` 判断逻辑
- [ ] 查看最近决策历史
- [ ] 验证重新思考是否触发

### 智能体无响应
- [ ] 确认消息正确发送
- [ ] 检查智能体输入处理
- [ ] 查看错误日志

### LLM 调用失败
- [ ] 验证 Ollama 服务状态
- [ ] 检查模型名称和参数
- [ ] 测试网络连接

## 📈 性能监控指标

### 执行效率
- 总 LLM 调用次数
- 平均响应时间
- 重新思考触发频率

### 决策质量
- 智能体选择分布
- 循环检测准确性
- 任务完成率

## 🔄 典型执行流程

1. **初始化阶段**
   - 收集任务事实 (`task_ledger_facts`)
   - 制定执行计划 (`task_ledger_plan`)

2. **执行阶段**
   - 评估进度 (`progress_ledger`)
   - 选择下一个智能体
   - 发送指令

3. **重新思考阶段**（如需要）
   - 更新事实清单 (`facts_update`)
   - 重新制定计划 (`plan_update`)

4. **完成阶段**
   - 生成最终答案 (`final_answer`)

## 💡 调试最佳实践

1. **从基础开始**: 先运行基础调试了解整体流程
2. **逐步深入**: 使用专门工具分析特定组件
3. **可视化理解**: 利用 HTML 报告直观理解消息流
4. **对比分析**: 比较不同场景的执行模式
5. **持续监控**: 关注关键指标的变化趋势

## 🆘 紧急问题解决

### Ollama 服务问题
```bash
# 启动 Ollama 服务
ollama serve

# 检查模型是否可用
ollama list

# 拉取模型（如需要）
ollama pull llama3.1
```

### 权限问题
```bash
# 确保脚本可执行
chmod +x scripts/*.py

# 检查日志目录权限
ls -la logs/
```

### 依赖问题
```bash
# 重新安装依赖
pip install -r requirements.txt

# 检查 AutoGen 版本
pip show autogen-ext
```

---

**记住**: 调试是一个迭代过程，从宏观到微观，从简单到复杂，逐步深入理解系统的工作机制。
